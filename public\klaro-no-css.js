!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.klaro=t():e.klaro=t()}(self,(()=>(()=>{var e={2690:(e,t,r)=>{"use strict";function n(){for(var e=document.cookie.split(";"),t=[],r=new RegExp("^\\s*([^=]+)\\s*=\\s*(.*?)$"),n=0;n<e.length;n++){var i=e[n],o=r.exec(i);null!==o&&t.push({name:o[1],value:o[2]})}return t}function i(e,t,r){var n=e+"=; Max-Age=-99999999;";document.cookie=n,n+=" path="+(t||"/")+";",document.cookie=n,void 0!==r&&(n+=" domain="+r+";",document.cookie=n)}r.d(t,{default:()=>C}),r(9305),r(2733),r(4701),r(1678),r(4776),r(4382),r(9892),r(4962),r(6584),r(9336),r(4754),r(1908),r(94),r(7132),r(6457),r(8908),r(3810),r(8557),r(646),r(5021),r(3687),r(2745),r(3994),r(3062),r(4062),r(3630),r(2367);var o=r(5482);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function s(e,t,r){return t=u(t),function(e,t){if(t&&("object"===a(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,c()?Reflect.construct(t,r||[],u(e).constructor):t.apply(e,r))}function c(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(c=function(){return!!e})()}function u(e){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},u(e)}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&p(e,t)}function p(e,t){return p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},p(e,t)}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,m(n.key),n)}}function v(e,t,r){return t&&d(e.prototype,t),r&&d(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function m(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==a(t)?t:String(t)}r(6437),r(2697),r(1359);var y=function(){function e(){f(this,e),this.value=null}return v(e,[{key:"get",value:function(){return this.value}},{key:"set",value:function(e){this.value=e}},{key:"delete",value:function(){this.value=null}}]),e}(),h=function(){function e(t){f(this,e),this.cookieName=t.storageName,this.cookieDomain=t.cookieDomain,this.cookiePath=t.cookiePath,this.cookieExpiresAfterDays=t.cookieExpiresAfterDays}return v(e,[{key:"get",value:function(){var e=function(e){for(var t=n(),r=0;r<t.length;r++)if(t[r].name===e)return t[r];return null}(this.cookieName);return e?e.value:null}},{key:"set",value:function(e){return function(e,t,r,n,i){var o="";if(r){var a=new Date;a.setTime(a.getTime()+24*r*60*60*1e3),o="; expires="+a.toUTCString()}void 0!==n&&(o+="; domain="+n),o+=void 0!==i?"; path="+i:"; path=/",document.cookie=e+"="+(t||"")+o+"; SameSite=Lax"}(this.cookieName,e,this.cookieExpiresAfterDays,this.cookieDomain,this.cookiePath)}},{key:"delete",value:function(){return i(this.cookieName)}}]),e}(),b=function(){function e(t,r){f(this,e),this.key=t.storageName,this.handle=r}return v(e,[{key:"get",value:function(){return this.handle.getItem(this.key)}},{key:"getWithKey",value:function(e){return this.handle.getItem(e)}},{key:"set",value:function(e){return this.handle.setItem(this.key,e)}},{key:"setWithKey",value:function(e,t){return this.handle.setItem(e,t)}},{key:"delete",value:function(){return this.handle.removeItem(this.key)}},{key:"deleteWithKey",value:function(e){return this.handle.removeItem(e)}}]),e}(),g=function(e){function t(e){return f(this,t),s(this,t,[e,localStorage])}return l(t,e),v(t)}(b),_=function(e){function t(e){return f(this,t),s(this,t,[e,sessionStorage])}return l(t,e),v(t)}(b);const w={cookie:h,test:y,localStorage:g,sessionStorage:_};function k(e){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},k(e)}function S(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=x(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw o}}}}function j(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,a,s=[],c=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(e,t)||x(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(e,t){if(e){if("string"==typeof e)return O(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?O(e,t):void 0}}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function P(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach((function(t){var n,i,o;n=e,i=t,o=r[t],(i=z(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function A(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,z(n.key),n)}}function z(e){var t=function(e,t){if("object"!=k(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=k(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==k(t)?t:String(t)}var C=function(){function e(t,r,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.config=t,this.store=void 0!==r?r:new w[this.storageMethod](this),void 0===this.store&&(this.store=w.cookie),this.auxiliaryStore=void 0!==n?n:new _(this),this.consents=this.defaultConsents,this.confirmed=!1,this.changed=!1,this.states={},this.initialized={},this.executedOnce={},this.watchers=new Set([]),this.loadConsents(),this.applyConsents(),this.savedConsents=P({},this.consents)}var t,r;return t=e,(r=[{key:"storageMethod",get:function(){return this.config.storageMethod||"cookie"}},{key:"storageName",get:function(){return this.config.storageName||this.config.cookieName||"klaro"}},{key:"cookieDomain",get:function(){return this.config.cookieDomain||void 0}},{key:"cookiePath",get:function(){return this.config.cookiePath||void 0}},{key:"cookieExpiresAfterDays",get:function(){return this.config.cookieExpiresAfterDays||120}},{key:"defaultConsents",get:function(){for(var e={},t=0;t<this.config.services.length;t++){var r=this.config.services[t];e[r.name]=this.getDefaultConsent(r)}return e}},{key:"watch",value:function(e){this.watchers.has(e)||this.watchers.add(e)}},{key:"unwatch",value:function(e){this.watchers.has(e)&&this.watchers.delete(e)}},{key:"notify",value:function(e,t){var r=this;this.watchers.forEach((function(n){n.update(r,e,t)}))}},{key:"getService",value:function(e){var t=this.config.services.filter((function(t){return t.name===e}));if(t.length>0)return t[0]}},{key:"getDefaultConsent",value:function(e){var t=e.default||e.required;return void 0===t&&(t=this.config.default),void 0===t&&(t=!1),t}},{key:"changeAll",value:function(e){var t=this,r=0;return this.config.services.filter((function(e){return!e.contextualConsentOnly})).map((function(n){n.required||t.config.required||e?t.updateConsent(n.name,!0)&&r++:t.updateConsent(n.name,!1)&&r++})),r}},{key:"updateConsent",value:function(e,t){var r=(this.consents[e]||!1)!==t;return this.consents[e]=t,this.notify("consents",this.consents),r}},{key:"resetConsents",value:function(){this.consents=this.defaultConsents,this.states={},this.confirmed=!1,this.applyConsents(),this.savedConsents=P({},this.consents),this.store.delete(),this.notify("consents",this.consents)}},{key:"getConsent",value:function(e){return this.consents[e]||!1}},{key:"loadConsents",value:function(){var e=this.store.get();return null!==e&&(this.consents=JSON.parse(decodeURIComponent(e)),this._checkConsents(),this.notify("consents",this.consents)),this.consents}},{key:"saveAndApplyConsents",value:function(e){this.saveConsents(e),this.applyConsents()}},{key:"changedConsents",value:function(){for(var e={},t=0,r=Object.entries(this.consents);t<r.length;t++){var n=j(r[t],2),i=n[0],o=n[1];this.savedConsents[i]!==o&&(e[i]=o)}return e}},{key:"saveConsents",value:function(e){var t=encodeURIComponent(JSON.stringify(this.consents));this.store.set(t),this.confirmed=!0,this.changed=!1;var r=this.changedConsents();this.savedConsents=P({},this.consents),this.notify("saveConsents",{changes:r,consents:this.consents,type:e||"script"})}},{key:"applyConsents",value:function(e,t,r){function n(e,t){if(void 0!==e)return("function"==typeof e?e:new Function("opts",e))(t)}for(var i=0,o=0;o<this.config.services.length;o++){var a=this.config.services[o];if(void 0===r||r===a.name){var s=a.vars||{},c={service:a,config:this.config,vars:s};this.initialized[a.name]||(this.initialized[a.name]=!0,n(a.onInit,c))}}for(var u=0;u<this.config.services.length;u++){var l=this.config.services[u];if(void 0===r||r===l.name){var p=this.states[l.name],f=l.vars||{},d=void 0!==l.optOut?l.optOut:this.config.optOut||!1,v=void 0!==l.required?l.required:this.config.required||!1,m=this.confirmed||d||e||t,y=this.getConsent(l.name)&&m||v,h={service:l,config:this.config,vars:f,consents:this.consents,confirmed:this.confirmed};p!==y&&i++,e||(n(y?l.onAccept:l.onDecline,h),this.updateServiceElements(l,y),this.updateServiceStorage(l,y),void 0!==l.callback&&l.callback(y,l),void 0!==this.config.callback&&this.config.callback(y,l),this.states[l.name]=y)}}return this.notify("applyConsents",i,r),i}},{key:"updateServiceElements",value:function(e,t){if(t){if(e.onlyOnce&&this.executedOnce[e.name])return;this.executedOnce[e.name]=!0}for(var r=document.querySelectorAll("[data-name='"+e.name+"']"),n=0;n<r.length;n++){var i=r[n],a=i.parentElement,s=(0,o.RT)(i),c=s.type,u=s.src,l=s.href,p=["href","src","type"];if("placeholder"!==c)if("IFRAME"===i.tagName){if(t&&i.src===u){console.debug("Skipping ".concat(i.tagName," for service ").concat(e.name,", as it already has the correct type..."));continue}var f,d=document.createElement(i.tagName),v=S(i.attributes);try{for(v.s();!(f=v.n()).done;){var m=f.value;d.setAttribute(m.name,m.value)}}catch(e){v.e(e)}finally{v.f()}d.innerText=i.innerText,d.text=i.text,t?(void 0!==s["original-display"]&&(d.style.display=s["original-display"]),void 0!==s.src&&(d.src=s.src)):(d.src="",void 0!==s["modified-by-klaro"]&&void 0!==s["original-display"]?d.setAttribute("data-original-display",s["original-display"]):(void 0!==i.style.display&&d.setAttribute("data-original-display",i.style.display),d.setAttribute("data-modified-by-klaro","yes")),d.style.display="none"),a.insertBefore(d,i),a.removeChild(i)}else if("SCRIPT"===i.tagName||"LINK"===i.tagName){if(t&&i.type===(c||"")&&i.src===u){console.debug("Skipping ".concat(i.tagName," for service ").concat(e.name,", as it already has the correct type or src..."));continue}var y,h=document.createElement(i.tagName),b=S(i.attributes);try{for(b.s();!(y=b.n()).done;){var g=y.value;h.setAttribute(g.name,g.value)}}catch(e){b.e(e)}finally{b.f()}h.innerText=i.innerText,h.text=i.text,t?(h.type=c||"",void 0!==u&&(h.src=u),void 0!==l&&(h.href=l)):h.type="text/plain",a.insertBefore(h,i),a.removeChild(i)}else{if(t){var _,w=S(p);try{for(w.s();!(_=w.n()).done;){var k=_.value,j=s[k];void 0!==j&&(void 0===s["original-"+k]&&(s["original-"+k]=i[k]),i[k]=j)}}catch(e){w.e(e)}finally{w.f()}void 0!==s.title&&(i.title=s.title),void 0!==s["original-display"]?i.style.display=s["original-display"]:i.style.removeProperty("display")}else{void 0!==s.title&&i.removeAttribute("title"),void 0===s["original-display"]&&void 0!==i.style.display&&(s["original-display"]=i.style.display),i.style.display="none";var x,O=S(p);try{for(O.s();!(x=O.n()).done;){var E=x.value;void 0!==s[E]&&(void 0!==s["original-"+E]?i[E]=s["original-"+E]:i.removeAttribute(E))}}catch(e){O.e(e)}finally{O.f()}}(0,o.X7)(s,i)}else t?(i.style.display="none",s["original-display"]=i.style.display):i.style.display=s["original-display"]||"block"}}},{key:"updateServiceStorage",value:function(e,t){if(!t&&void 0!==e.cookies&&e.cookies.length>0)for(var r=n(),o=0;o<e.cookies.length;o++){var a=e.cookies[o],s=void 0,c=void 0;if(a instanceof Array){var u=j(a,3);a=u[0],s=u[1],c=u[2]}else if(a instanceof Object&&!(a instanceof RegExp)){var l=a;a=l.pattern,s=l.path,c=l.domain}if(void 0!==a){a instanceof RegExp||(a=a.startsWith("^")?new RegExp(a):new RegExp("^"+a.replace(/[-[\]/{}()*+?.\\^$|]/g,"\\$&")+"$"));for(var p=0;p<r.length;p++){var f=r[p];null!==a.exec(f.name)&&(console.debug("Deleting cookie:",f.name,"Matched pattern:",a,"Path:",s,"Domain:",c),i(f.name,s,c),void 0===c&&i(f.name,s,"."+window.location.hostname))}}}}},{key:"_checkConsents",value:function(){for(var e=!0,t=new Set(this.config.services.map((function(e){return e.name}))),r=new Set(Object.keys(this.consents)),n=0,i=Object.keys(this.consents);n<i.length;n++){var o=i[n];t.has(o)||delete this.consents[o]}var a,s=S(this.config.services);try{for(s.s();!(a=s.n()).done;){var c=a.value;r.has(c.name)||(this.consents[c.name]=this.getDefaultConsent(c),e=!1)}}catch(e){s.e(e)}finally{s.f()}this.confirmed=e,e||(this.changed=!0)}}])&&A(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}()},5482:(e,t,r)=>{"use strict";function n(e,t){if(e){if("string"==typeof e)return i(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(e,t):void 0}}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function o(e){if(null!==document.currentScript&&void 0!==document.currentScript)return document.currentScript;for(var t=document.getElementsByTagName("script"),r=0;r<t.length;r++){var n=t[r];if(n.src.includes(e))return n}return null}function a(e){for(var t={},r=0;r<e.attributes.length;r++){var n=e.attributes[r];n.name.startsWith("data-")&&(t[n.name.slice(5)]=n.value)}return t}function s(e,t){for(var r=Object.keys(e),n=0;n<r.length;n++){var i=r[n],o=e[i];t[i]!==o&&t.setAttribute("data-"+i,o)}}function c(e){var t,r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=n(e))){r&&(e=r);var i=0,o=function(){};return{s:o,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){c=!0,a=e},f:function(){try{s||null==r.return||r.return()}finally{if(c)throw a}}}}(document.querySelectorAll("style[data-context=klaro-styles]"));try{for(r.s();!(t=r.n()).done;){var i=t.value,o=i.innerText;void 0!==i.styleSheet&&(o=i.styleSheet.cssText);for(var a=function(){var e,t,r=(e=c[s],t=2,function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,a,s=[],c=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(e,t)||n(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=r[0],a=r[1],u=new RegExp("([a-z0-9-]+):[^;]+;[\\s\\n]*\\1:\\s*var\\(--"+i+",\\s*[^\\)]+\\)","g");o=o.replace(u,(function(e,t){return"".concat(t,": ").concat(a,"; ").concat(t,": var(--").concat(i,", ").concat(a,")")}))},s=0,c=Object.entries(e);s<c.length;s++)a();var u=document.createElement("style");u.setAttribute("type","text/css"),u.setAttribute("data-context","klaro-styles"),void 0!==u.styleSheet?u.styleSheet.cssText=o:u.innerText=o,i.parentElement.appendChild(u),i.parentElement.removeChild(i)}}catch(e){r.e(e)}finally{r.f()}}r.d(t,{N3:()=>c,RT:()=>a,X7:()=>s,XZ:()=>o}),r(9305),r(2733),r(4701),r(4776),r(9892),r(6281),r(4962),r(9336),r(1908),r(7132),r(3810),r(8557),r(646),r(5021),r(3687),r(9425),r(3994),r(3062),r(4062),r(2367)},362:(e,t,r)=>{"use strict";var n=r(6441);function i(){}function o(){}o.resetWarningCache=i,e.exports=function(){function e(e,t,r,i,o,a){if(a!==n){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:i};return r.PropTypes=r,r}},2688:(e,t,r)=>{e.exports=r(362)()},6441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},8120:(e,t,r)=>{"use strict";var n=r(1483),i=r(8761),o=TypeError;e.exports=function(e){if(n(e))return e;throw new o(i(e)+" is not a function")}},2374:(e,t,r)=>{"use strict";var n=r(943),i=r(8761),o=TypeError;e.exports=function(e){if(n(e))return e;throw new o(i(e)+" is not a constructor")}},3852:(e,t,r)=>{"use strict";var n=r(735),i=String,o=TypeError;e.exports=function(e){if(n(e))return e;throw new o("Can't set "+i(e)+" as a prototype")}},7095:(e,t,r)=>{"use strict";var n=r(1),i=r(5290),o=r(5835).f,a=n("unscopables"),s=Array.prototype;void 0===s[a]&&o(s,a,{configurable:!0,value:i(null)}),e.exports=function(e){s[a][e]=!0}},4419:(e,t,r)=>{"use strict";var n=r(9105).charAt;e.exports=function(e,t,r){return t+(r?n(e,t).length:1)}},6021:(e,t,r)=>{"use strict";var n=r(4815),i=TypeError;e.exports=function(e,t){if(n(t,e))return e;throw new i("Incorrect invocation")}},2293:(e,t,r)=>{"use strict";var n=r(1704),i=String,o=TypeError;e.exports=function(e){if(n(e))return e;throw new o(i(e)+" is not an object")}},9214:(e,t,r)=>{"use strict";var n=r(8473);e.exports=n((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}}))},4793:(e,t,r)=>{"use strict";var n=r(2867).forEach,i=r(3152)("forEach");e.exports=i?[].forEach:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}},6142:(e,t,r)=>{"use strict";var n=r(2914),i=r(1807),o=r(2347),a=r(8901),s=r(5299),c=r(943),u=r(6960),l=r(670),p=r(4887),f=r(6665),d=Array;e.exports=function(e){var t=o(e),r=c(this),v=arguments.length,m=v>1?arguments[1]:void 0,y=void 0!==m;y&&(m=n(m,v>2?arguments[2]:void 0));var h,b,g,_,w,k,S=f(t),j=0;if(!S||this===d&&s(S))for(h=u(t),b=r?new this(h):d(h);h>j;j++)k=y?m(t[j],j):t[j],l(b,j,k);else for(w=(_=p(t,S)).next,b=r?new this:[];!(g=i(w,_)).done;j++)k=y?a(_,m,[g.value,j],!0):g.value,l(b,j,k);return b.length=j,b}},6651:(e,t,r)=>{"use strict";var n=r(5599),i=r(3392),o=r(6960),a=function(e){return function(t,r,a){var s=n(t),c=o(s);if(0===c)return!e&&-1;var u,l=i(a,c);if(e&&r!=r){for(;c>l;)if((u=s[l++])!=u)return!0}else for(;c>l;l++)if((e||l in s)&&s[l]===r)return e||l||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},2867:(e,t,r)=>{"use strict";var n=r(2914),i=r(4762),o=r(2121),a=r(2347),s=r(6960),c=r(4551),u=i([].push),l=function(e){var t=1===e,r=2===e,i=3===e,l=4===e,p=6===e,f=7===e,d=5===e||p;return function(v,m,y,h){for(var b,g,_=a(v),w=o(_),k=s(w),S=n(m,y),j=0,x=h||c,O=t?x(v,k):r||f?x(v,0):void 0;k>j;j++)if((d||j in w)&&(g=S(b=w[j],j,_),e))if(t)O[j]=g;else if(g)switch(e){case 3:return!0;case 5:return b;case 6:return j;case 2:u(O,b)}else switch(e){case 4:return!1;case 7:u(O,b)}return p?-1:i||l?l:O}};e.exports={forEach:l(0),map:l(1),filter:l(2),some:l(3),every:l(4),find:l(5),findIndex:l(6),filterReject:l(7)}},4595:(e,t,r)=>{"use strict";var n=r(8473),i=r(1),o=r(6170),a=i("species");e.exports=function(e){return o>=51||!n((function(){var t=[];return(t.constructor={})[a]=function(){return{foo:1}},1!==t[e](Boolean).foo}))}},3152:(e,t,r)=>{"use strict";var n=r(8473);e.exports=function(e,t){var r=[][e];return!!r&&n((function(){r.call(null,t||function(){return 1},1)}))}},1698:(e,t,r)=>{"use strict";var n=r(4762);e.exports=n([].slice)},7354:(e,t,r)=>{"use strict";var n=r(1698),i=Math.floor,o=function(e,t){var r=e.length;if(r<8)for(var a,s,c=1;c<r;){for(s=c,a=e[c];s&&t(e[s-1],a)>0;)e[s]=e[--s];s!==c++&&(e[s]=a)}else for(var u=i(r/2),l=o(n(e,0,u),t),p=o(n(e,u),t),f=l.length,d=p.length,v=0,m=0;v<f||m<d;)e[v+m]=v<f&&m<d?t(l[v],p[m])<=0?l[v++]:p[m++]:v<f?l[v++]:p[m++];return e};e.exports=o},9703:(e,t,r)=>{"use strict";var n=r(4914),i=r(943),o=r(1704),a=r(1)("species"),s=Array;e.exports=function(e){var t;return n(e)&&(t=e.constructor,(i(t)&&(t===s||n(t.prototype))||o(t)&&null===(t=t[a]))&&(t=void 0)),void 0===t?s:t}},4551:(e,t,r)=>{"use strict";var n=r(9703);e.exports=function(e,t){return new(n(e))(0===t?0:t)}},8901:(e,t,r)=>{"use strict";var n=r(2293),i=r(6721);e.exports=function(e,t,r,o){try{return o?t(n(r)[0],r[1]):t(r)}catch(t){i(e,"throw",t)}}},1554:(e,t,r)=>{"use strict";var n=r(1)("iterator"),i=!1;try{var o=0,a={next:function(){return{done:!!o++}},return:function(){i=!0}};a[n]=function(){return this},Array.from(a,(function(){throw 2}))}catch(e){}e.exports=function(e,t){try{if(!t&&!i)return!1}catch(e){return!1}var r=!1;try{var o={};o[n]=function(){return{next:function(){return{done:r=!0}}}},e(o)}catch(e){}return r}},1278:(e,t,r)=>{"use strict";var n=r(4762),i=n({}.toString),o=n("".slice);e.exports=function(e){return o(i(e),8,-1)}},6145:(e,t,r)=>{"use strict";var n=r(4338),i=r(1483),o=r(1278),a=r(1)("toStringTag"),s=Object,c="Arguments"===o(function(){return arguments}());e.exports=n?o:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=s(e),a))?r:c?o(t):"Object"===(n=o(t))&&i(t.callee)?"Arguments":n}},4092:(e,t,r)=>{"use strict";var n=r(5290),i=r(3864),o=r(2313),a=r(2914),s=r(6021),c=r(5983),u=r(1506),l=r(5662),p=r(5247),f=r(240),d=r(382),v=r(8041).fastKey,m=r(4483),y=m.set,h=m.getterFor;e.exports={getConstructor:function(e,t,r,l){var p=e((function(e,i){s(e,f),y(e,{type:t,index:n(null),first:void 0,last:void 0,size:0}),d||(e.size=0),c(i)||u(i,e[l],{that:e,AS_ENTRIES:r})})),f=p.prototype,m=h(t),b=function(e,t,r){var n,i,o=m(e),a=g(e,t);return a?a.value=r:(o.last=a={index:i=v(t,!0),key:t,value:r,previous:n=o.last,next:void 0,removed:!1},o.first||(o.first=a),n&&(n.next=a),d?o.size++:e.size++,"F"!==i&&(o.index[i]=a)),e},g=function(e,t){var r,n=m(e),i=v(t);if("F"!==i)return n.index[i];for(r=n.first;r;r=r.next)if(r.key===t)return r};return o(f,{clear:function(){for(var e=m(this),t=e.first;t;)t.removed=!0,t.previous&&(t.previous=t.previous.next=void 0),t=t.next;e.first=e.last=void 0,e.index=n(null),d?e.size=0:this.size=0},delete:function(e){var t=this,r=m(t),n=g(t,e);if(n){var i=n.next,o=n.previous;delete r.index[n.index],n.removed=!0,o&&(o.next=i),i&&(i.previous=o),r.first===n&&(r.first=i),r.last===n&&(r.last=o),d?r.size--:t.size--}return!!n},forEach:function(e){for(var t,r=m(this),n=a(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:r.first;)for(n(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!g(this,e)}}),o(f,r?{get:function(e){var t=g(this,e);return t&&t.value},set:function(e,t){return b(this,0===e?0:e,t)}}:{add:function(e){return b(this,e=0===e?0:e,e)}}),d&&i(f,"size",{configurable:!0,get:function(){return m(this).size}}),p},setStrong:function(e,t,r){var n=t+" Iterator",i=h(t),o=h(n);l(e,t,(function(e,t){y(this,{type:n,target:e,state:i(e),kind:t,last:void 0})}),(function(){for(var e=o(this),t=e.kind,r=e.last;r&&r.removed;)r=r.previous;return e.target&&(e.last=r=r?r.next:e.state.first)?p("keys"===t?r.key:"values"===t?r.value:[r.key,r.value],!1):(e.target=void 0,p(void 0,!0))}),r?"entries":"values",!r,!0),f(t)}}},7446:(e,t,r)=>{"use strict";var n=r(8612),i=r(8389),o=r(4762),a=r(8730),s=r(7914),c=r(8041),u=r(1506),l=r(6021),p=r(1483),f=r(5983),d=r(1704),v=r(8473),m=r(1554),y=r(2277),h=r(2429);e.exports=function(e,t,r){var b=-1!==e.indexOf("Map"),g=-1!==e.indexOf("Weak"),_=b?"set":"add",w=i[e],k=w&&w.prototype,S=w,j={},x=function(e){var t=o(k[e]);s(k,e,"add"===e?function(e){return t(this,0===e?0:e),this}:"delete"===e?function(e){return!(g&&!d(e))&&t(this,0===e?0:e)}:"get"===e?function(e){return g&&!d(e)?void 0:t(this,0===e?0:e)}:"has"===e?function(e){return!(g&&!d(e))&&t(this,0===e?0:e)}:function(e,r){return t(this,0===e?0:e,r),this})};if(a(e,!p(w)||!(g||k.forEach&&!v((function(){(new w).entries().next()})))))S=r.getConstructor(t,e,b,_),c.enable();else if(a(e,!0)){var O=new S,E=O[_](g?{}:-0,1)!==O,P=v((function(){O.has(1)})),A=m((function(e){new w(e)})),z=!g&&v((function(){for(var e=new w,t=5;t--;)e[_](t,t);return!e.has(-0)}));A||((S=t((function(e,t){l(e,k);var r=h(new w,e,S);return f(t)||u(t,r[_],{that:r,AS_ENTRIES:b}),r}))).prototype=k,k.constructor=S),(P||z)&&(x("delete"),x("has"),b&&x("get")),(z||E)&&x(_),g&&k.clear&&delete k.clear}return j[e]=S,n({global:!0,constructor:!0,forced:S!==w},j),y(S,e),g||r.setStrong(S,e,b),S}},6726:(e,t,r)=>{"use strict";var n=r(5755),i=r(9497),o=r(4961),a=r(5835);e.exports=function(e,t,r){for(var s=i(t),c=a.f,u=o.f,l=0;l<s.length;l++){var p=s[l];n(e,p)||r&&n(r,p)||c(e,p,u(t,p))}}},4522:(e,t,r)=>{"use strict";var n=r(1)("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[n]=!1,"/./"[e](t)}catch(e){}}return!1}},9441:(e,t,r)=>{"use strict";var n=r(8473);e.exports=!n((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},5247:e=>{"use strict";e.exports=function(e,t){return{value:e,done:t}}},9037:(e,t,r)=>{"use strict";var n=r(382),i=r(5835),o=r(7738);e.exports=n?function(e,t,r){return i.f(e,t,o(1,r))}:function(e,t,r){return e[t]=r,e}},7738:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},670:(e,t,r)=>{"use strict";var n=r(382),i=r(5835),o=r(7738);e.exports=function(e,t,r){n?i.f(e,t,o(0,r)):e[t]=r}},6446:(e,t,r)=>{"use strict";var n=r(2293),i=r(348),o=TypeError;e.exports=function(e){if(n(this),"string"===e||"default"===e)e="string";else if("number"!==e)throw new o("Incorrect hint");return i(this,e)}},3864:(e,t,r)=>{"use strict";var n=r(169),i=r(5835);e.exports=function(e,t,r){return r.get&&n(r.get,t,{getter:!0}),r.set&&n(r.set,t,{setter:!0}),i.f(e,t,r)}},7914:(e,t,r)=>{"use strict";var n=r(1483),i=r(5835),o=r(169),a=r(2095);e.exports=function(e,t,r,s){s||(s={});var c=s.enumerable,u=void 0!==s.name?s.name:t;if(n(r)&&o(r,u,s),s.global)c?e[t]=r:a(t,r);else{try{s.unsafe?e[t]&&(c=!0):delete e[t]}catch(e){}c?e[t]=r:i.f(e,t,{value:r,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return e}},2313:(e,t,r)=>{"use strict";var n=r(7914);e.exports=function(e,t,r){for(var i in t)n(e,i,t[i],r);return e}},2095:(e,t,r)=>{"use strict";var n=r(8389),i=Object.defineProperty;e.exports=function(e,t){try{i(n,e,{value:t,configurable:!0,writable:!0})}catch(r){n[e]=t}return t}},6060:(e,t,r)=>{"use strict";var n=r(8761),i=TypeError;e.exports=function(e,t){if(!delete e[t])throw new i("Cannot delete property "+n(t)+" of "+n(e))}},382:(e,t,r)=>{"use strict";var n=r(8473);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},3145:(e,t,r)=>{"use strict";var n=r(8389),i=r(1704),o=n.document,a=i(o)&&i(o.createElement);e.exports=function(e){return a?o.createElement(e):{}}},1091:e=>{"use strict";var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},4842:e=>{"use strict";e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},1902:(e,t,r)=>{"use strict";var n=r(3145)("span").classList,i=n&&n.constructor&&n.constructor.prototype;e.exports=i===Object.prototype?void 0:i},7332:(e,t,r)=>{"use strict";var n=r(9966).match(/firefox\/(\d+)/i);e.exports=!!n&&+n[1]},6956:(e,t,r)=>{"use strict";var n=r(938),i=r(4334);e.exports=!n&&!i&&"object"==typeof window&&"object"==typeof document},5413:e=>{"use strict";e.exports="function"==typeof Bun&&Bun&&"string"==typeof Bun.version},938:e=>{"use strict";e.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},8996:(e,t,r)=>{"use strict";var n=r(9966);e.exports=/MSIE|Trident/.test(n)},4466:(e,t,r)=>{"use strict";var n=r(9966);e.exports=/ipad|iphone|ipod/i.test(n)&&"undefined"!=typeof Pebble},8417:(e,t,r)=>{"use strict";var n=r(9966);e.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(n)},4334:(e,t,r)=>{"use strict";var n=r(8389),i=r(1278);e.exports="process"===i(n.process)},6639:(e,t,r)=>{"use strict";var n=r(9966);e.exports=/web0s(?!.*chrome)/i.test(n)},9966:e=>{"use strict";e.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},6170:(e,t,r)=>{"use strict";var n,i,o=r(8389),a=r(9966),s=o.process,c=o.Deno,u=s&&s.versions||c&&c.version,l=u&&u.v8;l&&(i=(n=l.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!i&&a&&(!(n=a.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/))&&(i=+n[1]),e.exports=i},5158:(e,t,r)=>{"use strict";var n=r(9966).match(/AppleWebKit\/(\d+)\./);e.exports=!!n&&+n[1]},4741:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8612:(e,t,r)=>{"use strict";var n=r(8389),i=r(4961).f,o=r(9037),a=r(7914),s=r(2095),c=r(6726),u=r(8730);e.exports=function(e,t){var r,l,p,f,d,v=e.target,m=e.global,y=e.stat;if(r=m?n:y?n[v]||s(v,{}):n[v]&&n[v].prototype)for(l in t){if(f=t[l],p=e.dontCallGetSet?(d=i(r,l))&&d.value:r[l],!u(m?l:v+(y?".":"#")+l,e.forced)&&void 0!==p){if(typeof f==typeof p)continue;c(f,p)}(e.sham||p&&p.sham)&&o(f,"sham",!0),a(r,l,f,e)}}},8473:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},3358:(e,t,r)=>{"use strict";r(5021);var n=r(1807),i=r(7914),o=r(8865),a=r(8473),s=r(1),c=r(9037),u=s("species"),l=RegExp.prototype;e.exports=function(e,t,r,p){var f=s(e),d=!a((function(){var t={};return t[f]=function(){return 7},7!==""[e](t)})),v=d&&!a((function(){var t=!1,r=/a/;return"split"===e&&((r={}).constructor={},r.constructor[u]=function(){return r},r.flags="",r[f]=/./[f]),r.exec=function(){return t=!0,null},r[f](""),!t}));if(!d||!v||r){var m=/./[f],y=t(f,""[e],(function(e,t,r,i,a){var s=t.exec;return s===o||s===l.exec?d&&!a?{done:!0,value:n(m,t,r,i)}:{done:!0,value:n(e,r,t,i)}:{done:!1}}));i(String.prototype,e,y[0]),i(l,f,y[1])}p&&c(l[f],"sham",!0)}},6530:(e,t,r)=>{"use strict";var n=r(8473);e.exports=!n((function(){return Object.isExtensible(Object.preventExtensions({}))}))},3067:(e,t,r)=>{"use strict";var n=r(274),i=Function.prototype,o=i.apply,a=i.call;e.exports="object"==typeof Reflect&&Reflect.apply||(n?a.bind(o):function(){return a.apply(o,arguments)})},2914:(e,t,r)=>{"use strict";var n=r(3786),i=r(8120),o=r(274),a=n(n.bind);e.exports=function(e,t){return i(e),void 0===t?e:o?a(e,t):function(){return e.apply(t,arguments)}}},274:(e,t,r)=>{"use strict";var n=r(8473);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},2164:(e,t,r)=>{"use strict";var n=r(4762),i=r(8120),o=r(1704),a=r(5755),s=r(1698),c=r(274),u=Function,l=n([].concat),p=n([].join),f={};e.exports=c?u.bind:function(e){var t=i(this),r=t.prototype,n=s(arguments,1),c=function(){var r=l(n,s(arguments));return this instanceof c?function(e,t,r){if(!a(f,t)){for(var n=[],i=0;i<t;i++)n[i]="a["+i+"]";f[t]=u("C,a","return new C("+p(n,",")+")")}return f[t](e,r)}(t,r.length,r):t.apply(e,r)};return o(r)&&(c.prototype=r),c}},1807:(e,t,r)=>{"use strict";var n=r(274),i=Function.prototype.call;e.exports=n?i.bind(i):function(){return i.apply(i,arguments)}},2048:(e,t,r)=>{"use strict";var n=r(382),i=r(5755),o=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,s=i(o,"name"),c=s&&"something"===function(){}.name,u=s&&(!n||n&&a(o,"name").configurable);e.exports={EXISTS:s,PROPER:c,CONFIGURABLE:u}},680:(e,t,r)=>{"use strict";var n=r(4762),i=r(8120);e.exports=function(e,t,r){try{return n(i(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(e){}}},3786:(e,t,r)=>{"use strict";var n=r(1278),i=r(4762);e.exports=function(e){if("Function"===n(e))return i(e)}},4762:(e,t,r)=>{"use strict";var n=r(274),i=Function.prototype,o=i.call,a=n&&i.bind.bind(o,o);e.exports=n?a:function(e){return function(){return o.apply(e,arguments)}}},1409:(e,t,r)=>{"use strict";var n=r(8389),i=r(1483);e.exports=function(e,t){return arguments.length<2?(r=n[e],i(r)?r:void 0):n[e]&&n[e][t];var r}},6665:(e,t,r)=>{"use strict";var n=r(6145),i=r(2564),o=r(5983),a=r(6775),s=r(1)("iterator");e.exports=function(e){if(!o(e))return i(e,s)||i(e,"@@iterator")||a[n(e)]}},4887:(e,t,r)=>{"use strict";var n=r(1807),i=r(8120),o=r(2293),a=r(8761),s=r(6665),c=TypeError;e.exports=function(e,t){var r=arguments.length<2?s(e):t;if(i(r))return o(n(r,e));throw new c(a(e)+" is not iterable")}},5215:(e,t,r)=>{"use strict";var n=r(4762),i=r(4914),o=r(1483),a=r(1278),s=r(6261),c=n([].push);e.exports=function(e){if(o(e))return e;if(i(e)){for(var t=e.length,r=[],n=0;n<t;n++){var u=e[n];"string"==typeof u?c(r,u):"number"!=typeof u&&"Number"!==a(u)&&"String"!==a(u)||c(r,s(u))}var l=r.length,p=!0;return function(e,t){if(p)return p=!1,t;if(i(this))return t;for(var n=0;n<l;n++)if(r[n]===e)return t}}}},2564:(e,t,r)=>{"use strict";var n=r(8120),i=r(5983);e.exports=function(e,t){var r=e[t];return i(r)?void 0:n(r)}},708:(e,t,r)=>{"use strict";var n=r(4762),i=r(2347),o=Math.floor,a=n("".charAt),s=n("".replace),c=n("".slice),u=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,l=/\$([$&'`]|\d{1,2})/g;e.exports=function(e,t,r,n,p,f){var d=r+e.length,v=n.length,m=l;return void 0!==p&&(p=i(p),m=u),s(f,m,(function(i,s){var u;switch(a(s,0)){case"$":return"$";case"&":return e;case"`":return c(t,0,r);case"'":return c(t,d);case"<":u=p[c(s,1,-1)];break;default:var l=+s;if(0===l)return i;if(l>v){var f=o(l/10);return 0===f?i:f<=v?void 0===n[f-1]?a(s,1):n[f-1]+a(s,1):i}u=n[l-1]}return void 0===u?"":u}))}},8389:function(e,t,r){"use strict";var n=function(e){return e&&e.Math===Math&&e};e.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},5755:(e,t,r)=>{"use strict";var n=r(4762),i=r(2347),o=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return o(i(e),t)}},1507:e=>{"use strict";e.exports={}},1339:e=>{"use strict";e.exports=function(e,t){try{1===arguments.length?console.error(e):console.error(e,t)}catch(e){}}},2811:(e,t,r)=>{"use strict";var n=r(1409);e.exports=n("document","documentElement")},1799:(e,t,r)=>{"use strict";var n=r(382),i=r(8473),o=r(3145);e.exports=!n&&!i((function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},2121:(e,t,r)=>{"use strict";var n=r(4762),i=r(8473),o=r(1278),a=Object,s=n("".split);e.exports=i((function(){return!a("z").propertyIsEnumerable(0)}))?function(e){return"String"===o(e)?s(e,""):a(e)}:a},2429:(e,t,r)=>{"use strict";var n=r(1483),i=r(1704),o=r(1953);e.exports=function(e,t,r){var a,s;return o&&n(a=t.constructor)&&a!==r&&i(s=a.prototype)&&s!==r.prototype&&o(e,s),e}},7268:(e,t,r)=>{"use strict";var n=r(4762),i=r(1483),o=r(1831),a=n(Function.toString);i(o.inspectSource)||(o.inspectSource=function(e){return a(e)}),e.exports=o.inspectSource},8041:(e,t,r)=>{"use strict";var n=r(8612),i=r(4762),o=r(1507),a=r(1704),s=r(5755),c=r(5835).f,u=r(2278),l=r(2020),p=r(706),f=r(1866),d=r(6530),v=!1,m=f("meta"),y=0,h=function(e){c(e,m,{value:{objectID:"O"+y++,weakData:{}}})},b=e.exports={enable:function(){b.enable=function(){},v=!0;var e=u.f,t=i([].splice),r={};r[m]=1,e(r).length&&(u.f=function(r){for(var n=e(r),i=0,o=n.length;i<o;i++)if(n[i]===m){t(n,i,1);break}return n},n({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:l.f}))},fastKey:function(e,t){if(!a(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!s(e,m)){if(!p(e))return"F";if(!t)return"E";h(e)}return e[m].objectID},getWeakData:function(e,t){if(!s(e,m)){if(!p(e))return!0;if(!t)return!1;h(e)}return e[m].weakData},onFreeze:function(e){return d&&v&&p(e)&&!s(e,m)&&h(e),e}};o[m]=!0},4483:(e,t,r)=>{"use strict";var n,i,o,a=r(4644),s=r(8389),c=r(1704),u=r(9037),l=r(5755),p=r(1831),f=r(5409),d=r(1507),v="Object already initialized",m=s.TypeError,y=s.WeakMap;if(a||p.state){var h=p.state||(p.state=new y);h.get=h.get,h.has=h.has,h.set=h.set,n=function(e,t){if(h.has(e))throw new m(v);return t.facade=e,h.set(e,t),t},i=function(e){return h.get(e)||{}},o=function(e){return h.has(e)}}else{var b=f("state");d[b]=!0,n=function(e,t){if(l(e,b))throw new m(v);return t.facade=e,u(e,b,t),t},i=function(e){return l(e,b)?e[b]:{}},o=function(e){return l(e,b)}}e.exports={set:n,get:i,has:o,enforce:function(e){return o(e)?i(e):n(e,{})},getterFor:function(e){return function(t){var r;if(!c(t)||(r=i(t)).type!==e)throw new m("Incompatible receiver, "+e+" required");return r}}}},5299:(e,t,r)=>{"use strict";var n=r(1),i=r(6775),o=n("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(i.Array===e||a[o]===e)}},4914:(e,t,r)=>{"use strict";var n=r(1278);e.exports=Array.isArray||function(e){return"Array"===n(e)}},1483:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},943:(e,t,r)=>{"use strict";var n=r(4762),i=r(8473),o=r(1483),a=r(6145),s=r(1409),c=r(7268),u=function(){},l=s("Reflect","construct"),p=/^\s*(?:class|function)\b/,f=n(p.exec),d=!p.test(u),v=function(e){if(!o(e))return!1;try{return l(u,[],e),!0}catch(e){return!1}},m=function(e){if(!o(e))return!1;switch(a(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return d||!!f(p,c(e))}catch(e){return!0}};m.sham=!0,e.exports=!l||i((function(){var e;return v(v.call)||!v(Object)||!v((function(){e=!0}))||e}))?m:v},8730:(e,t,r)=>{"use strict";var n=r(8473),i=r(1483),o=/#|\.prototype\./,a=function(e,t){var r=c[s(e)];return r===l||r!==u&&(i(t)?n(t):!!t)},s=a.normalize=function(e){return String(e).replace(o,".").toLowerCase()},c=a.data={},u=a.NATIVE="N",l=a.POLYFILL="P";e.exports=a},5983:e=>{"use strict";e.exports=function(e){return null==e}},1704:(e,t,r)=>{"use strict";var n=r(1483);e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},735:(e,t,r)=>{"use strict";var n=r(1704);e.exports=function(e){return n(e)||null===e}},9557:e=>{"use strict";e.exports=!1},4786:(e,t,r)=>{"use strict";var n=r(1704),i=r(1278),o=r(1)("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[o])?!!t:"RegExp"===i(e))}},1423:(e,t,r)=>{"use strict";var n=r(1409),i=r(1483),o=r(4815),a=r(5022),s=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return i(t)&&o(t.prototype,s(e))}},1506:(e,t,r)=>{"use strict";var n=r(2914),i=r(1807),o=r(2293),a=r(8761),s=r(5299),c=r(6960),u=r(4815),l=r(4887),p=r(6665),f=r(6721),d=TypeError,v=function(e,t){this.stopped=e,this.result=t},m=v.prototype;e.exports=function(e,t,r){var y,h,b,g,_,w,k,S=r&&r.that,j=!(!r||!r.AS_ENTRIES),x=!(!r||!r.IS_RECORD),O=!(!r||!r.IS_ITERATOR),E=!(!r||!r.INTERRUPTED),P=n(t,S),A=function(e){return y&&f(y,"normal",e),new v(!0,e)},z=function(e){return j?(o(e),E?P(e[0],e[1],A):P(e[0],e[1])):E?P(e,A):P(e)};if(x)y=e.iterator;else if(O)y=e;else{if(!(h=p(e)))throw new d(a(e)+" is not iterable");if(s(h)){for(b=0,g=c(e);g>b;b++)if((_=z(e[b]))&&u(m,_))return _;return new v(!1)}y=l(e,h)}for(w=x?e.next:y.next;!(k=i(w,y)).done;){try{_=z(k.value)}catch(e){f(y,"throw",e)}if("object"==typeof _&&_&&u(m,_))return _}return new v(!1)}},6721:(e,t,r)=>{"use strict";var n=r(1807),i=r(2293),o=r(2564);e.exports=function(e,t,r){var a,s;i(e);try{if(!(a=o(e,"return"))){if("throw"===t)throw r;return r}a=n(a,e)}catch(e){s=!0,a=e}if("throw"===t)throw r;if(s)throw a;return i(a),r}},1040:(e,t,r)=>{"use strict";var n=r(1851).IteratorPrototype,i=r(5290),o=r(7738),a=r(2277),s=r(6775),c=function(){return this};e.exports=function(e,t,r,u){var l=t+" Iterator";return e.prototype=i(n,{next:o(+!u,r)}),a(e,l,!1,!0),s[l]=c,e}},5662:(e,t,r)=>{"use strict";var n=r(8612),i=r(1807),o=r(9557),a=r(2048),s=r(1483),c=r(1040),u=r(3181),l=r(1953),p=r(2277),f=r(9037),d=r(7914),v=r(1),m=r(6775),y=r(1851),h=a.PROPER,b=a.CONFIGURABLE,g=y.IteratorPrototype,_=y.BUGGY_SAFARI_ITERATORS,w=v("iterator"),k="keys",S="values",j="entries",x=function(){return this};e.exports=function(e,t,r,a,v,y,O){c(r,t,a);var E,P,A,z=function(e){if(e===v&&D)return D;if(!_&&e&&e in N)return N[e];switch(e){case k:case S:case j:return function(){return new r(this,e)}}return function(){return new r(this)}},C=t+" Iterator",T=!1,N=e.prototype,I=N[w]||N["@@iterator"]||v&&N[v],D=!_&&I||z(v),R="Array"===t&&N.entries||I;if(R&&(E=u(R.call(new e)))!==Object.prototype&&E.next&&(o||u(E)===g||(l?l(E,g):s(E[w])||d(E,w,x)),p(E,C,!0,!0),o&&(m[C]=x)),h&&v===S&&I&&I.name!==S&&(!o&&b?f(N,"name",S):(T=!0,D=function(){return i(I,this)})),v)if(P={values:z(S),keys:y?D:z(k),entries:z(j)},O)for(A in P)(_||T||!(A in N))&&d(N,A,P[A]);else n({target:t,proto:!0,forced:_||T},P);return o&&!O||N[w]===D||d(N,w,D,{name:v}),m[t]=D,P}},1851:(e,t,r)=>{"use strict";var n,i,o,a=r(8473),s=r(1483),c=r(1704),u=r(5290),l=r(3181),p=r(7914),f=r(1),d=r(9557),v=f("iterator"),m=!1;[].keys&&("next"in(o=[].keys())?(i=l(l(o)))!==Object.prototype&&(n=i):m=!0),!c(n)||a((function(){var e={};return n[v].call(e)!==e}))?n={}:d&&(n=u(n)),s(n[v])||p(n,v,(function(){return this})),e.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:m}},6775:e=>{"use strict";e.exports={}},6960:(e,t,r)=>{"use strict";var n=r(8324);e.exports=function(e){return n(e.length)}},169:(e,t,r)=>{"use strict";var n=r(4762),i=r(8473),o=r(1483),a=r(5755),s=r(382),c=r(2048).CONFIGURABLE,u=r(7268),l=r(4483),p=l.enforce,f=l.get,d=String,v=Object.defineProperty,m=n("".slice),y=n("".replace),h=n([].join),b=s&&!i((function(){return 8!==v((function(){}),"length",{value:8}).length})),g=String(String).split("String"),_=e.exports=function(e,t,r){"Symbol("===m(d(t),0,7)&&(t="["+y(d(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!a(e,"name")||c&&e.name!==t)&&(s?v(e,"name",{value:t,configurable:!0}):e.name=t),b&&r&&a(r,"arity")&&e.length!==r.arity&&v(e,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?s&&v(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var n=p(e);return a(n,"source")||(n.source=h(g,"string"==typeof t?t:"")),e};Function.prototype.toString=_((function(){return o(this)&&f(this).source||u(this)}),"toString")},1703:e=>{"use strict";var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){var n=+e;return(n>0?r:t)(n)}},553:(e,t,r)=>{"use strict";var n,i,o,a,s,c=r(8389),u=r(8123),l=r(2914),p=r(7007).set,f=r(5459),d=r(8417),v=r(4466),m=r(6639),y=r(4334),h=c.MutationObserver||c.WebKitMutationObserver,b=c.document,g=c.process,_=c.Promise,w=u("queueMicrotask");if(!w){var k=new f,S=function(){var e,t;for(y&&(e=g.domain)&&e.exit();t=k.get();)try{t()}catch(e){throw k.head&&n(),e}e&&e.enter()};d||y||m||!h||!b?!v&&_&&_.resolve?((a=_.resolve(void 0)).constructor=_,s=l(a.then,a),n=function(){s(S)}):y?n=function(){g.nextTick(S)}:(p=l(p,c),n=function(){p(S)}):(i=!0,o=b.createTextNode(""),new h(S).observe(o,{characterData:!0}),n=function(){o.data=i=!i}),w=function(e){k.head||n(),k.add(e)}}e.exports=w},1173:(e,t,r)=>{"use strict";var n=r(8120),i=TypeError,o=function(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw new i("Bad Promise constructor");t=e,r=n})),this.resolve=n(t),this.reject=n(r)};e.exports.f=function(e){return new o(e)}},4989:(e,t,r)=>{"use strict";var n=r(4786),i=TypeError;e.exports=function(e){if(n(e))throw new i("The method doesn't accept regular expressions");return e}},1439:(e,t,r)=>{"use strict";var n=r(382),i=r(4762),o=r(1807),a=r(8473),s=r(3658),c=r(4347),u=r(7611),l=r(2347),p=r(2121),f=Object.assign,d=Object.defineProperty,v=i([].concat);e.exports=!f||a((function(){if(n&&1!==f({b:1},f(d({},"a",{enumerable:!0,get:function(){d(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},r=Symbol("assign detection"),i="abcdefghijklmnopqrst";return e[r]=7,i.split("").forEach((function(e){t[e]=e})),7!==f({},e)[r]||s(f({},t)).join("")!==i}))?function(e,t){for(var r=l(e),i=arguments.length,a=1,f=c.f,d=u.f;i>a;)for(var m,y=p(arguments[a++]),h=f?v(s(y),f(y)):s(y),b=h.length,g=0;b>g;)m=h[g++],n&&!o(d,y,m)||(r[m]=y[m]);return r}:f},5290:(e,t,r)=>{"use strict";var n,i=r(2293),o=r(5799),a=r(4741),s=r(1507),c=r(2811),u=r(3145),l=r(5409),p="prototype",f="script",d=l("IE_PROTO"),v=function(){},m=function(e){return"<"+f+">"+e+"</"+f+">"},y=function(e){e.write(m("")),e.close();var t=e.parentWindow.Object;return e=null,t},h=function(){try{n=new ActiveXObject("htmlfile")}catch(e){}var e,t,r;h="undefined"!=typeof document?document.domain&&n?y(n):(t=u("iframe"),r="java"+f+":",t.style.display="none",c.appendChild(t),t.src=String(r),(e=t.contentWindow.document).open(),e.write(m("document.F=Object")),e.close(),e.F):y(n);for(var i=a.length;i--;)delete h[p][a[i]];return h()};s[d]=!0,e.exports=Object.create||function(e,t){var r;return null!==e?(v[p]=i(e),r=new v,v[p]=null,r[d]=e):r=h(),void 0===t?r:o.f(r,t)}},5799:(e,t,r)=>{"use strict";var n=r(382),i=r(3896),o=r(5835),a=r(2293),s=r(5599),c=r(3658);t.f=n&&!i?Object.defineProperties:function(e,t){a(e);for(var r,n=s(t),i=c(t),u=i.length,l=0;u>l;)o.f(e,r=i[l++],n[r]);return e}},5835:(e,t,r)=>{"use strict";var n=r(382),i=r(1799),o=r(3896),a=r(2293),s=r(3815),c=TypeError,u=Object.defineProperty,l=Object.getOwnPropertyDescriptor,p="enumerable",f="configurable",d="writable";t.f=n?o?function(e,t,r){if(a(e),t=s(t),a(r),"function"==typeof e&&"prototype"===t&&"value"in r&&d in r&&!r[d]){var n=l(e,t);n&&n[d]&&(e[t]=r.value,r={configurable:f in r?r[f]:n[f],enumerable:p in r?r[p]:n[p],writable:!1})}return u(e,t,r)}:u:function(e,t,r){if(a(e),t=s(t),a(r),i)try{return u(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new c("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},4961:(e,t,r)=>{"use strict";var n=r(382),i=r(1807),o=r(7611),a=r(7738),s=r(5599),c=r(3815),u=r(5755),l=r(1799),p=Object.getOwnPropertyDescriptor;t.f=n?p:function(e,t){if(e=s(e),t=c(t),l)try{return p(e,t)}catch(e){}if(u(e,t))return a(!i(o.f,e,t),e[t])}},2020:(e,t,r)=>{"use strict";var n=r(1278),i=r(5599),o=r(2278).f,a=r(1698),s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return s&&"Window"===n(e)?function(e){try{return o(e)}catch(e){return a(s)}}(e):o(i(e))}},2278:(e,t,r)=>{"use strict";var n=r(6742),i=r(4741).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,i)}},4347:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},3181:(e,t,r)=>{"use strict";var n=r(5755),i=r(1483),o=r(2347),a=r(5409),s=r(9441),c=a("IE_PROTO"),u=Object,l=u.prototype;e.exports=s?u.getPrototypeOf:function(e){var t=o(e);if(n(t,c))return t[c];var r=t.constructor;return i(r)&&t instanceof r?r.prototype:t instanceof u?l:null}},706:(e,t,r)=>{"use strict";var n=r(8473),i=r(1704),o=r(1278),a=r(9214),s=Object.isExtensible,c=n((function(){s(1)}));e.exports=c||a?function(e){return!!i(e)&&(!a||"ArrayBuffer"!==o(e))&&(!s||s(e))}:s},4815:(e,t,r)=>{"use strict";var n=r(4762);e.exports=n({}.isPrototypeOf)},6742:(e,t,r)=>{"use strict";var n=r(4762),i=r(5755),o=r(5599),a=r(6651).indexOf,s=r(1507),c=n([].push);e.exports=function(e,t){var r,n=o(e),u=0,l=[];for(r in n)!i(s,r)&&i(n,r)&&c(l,r);for(;t.length>u;)i(n,r=t[u++])&&(~a(l,r)||c(l,r));return l}},3658:(e,t,r)=>{"use strict";var n=r(6742),i=r(4741);e.exports=Object.keys||function(e){return n(e,i)}},7611:(e,t)=>{"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,i=n&&!r.call({1:2},1);t.f=i?function(e){var t=n(this,e);return!!t&&t.enumerable}:r},1953:(e,t,r)=>{"use strict";var n=r(680),i=r(2293),o=r(3852);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=n(Object.prototype,"__proto__","set"))(r,[]),t=r instanceof Array}catch(e){}return function(r,n){return i(r),o(n),t?e(r,n):r.__proto__=n,r}}():void 0)},5627:(e,t,r)=>{"use strict";var n=r(382),i=r(8473),o=r(4762),a=r(3181),s=r(3658),c=r(5599),u=o(r(7611).f),l=o([].push),p=n&&i((function(){var e=Object.create(null);return e[2]=2,!u(e,2)})),f=function(e){return function(t){for(var r,i=c(t),o=s(i),f=p&&null===a(i),d=o.length,v=0,m=[];d>v;)r=o[v++],n&&!(f?r in i:u(i,r))||l(m,e?[r,i[r]]:i[r]);return m}};e.exports={entries:f(!0),values:f(!1)}},5685:(e,t,r)=>{"use strict";var n=r(4338),i=r(6145);e.exports=n?{}.toString:function(){return"[object "+i(this)+"]"}},348:(e,t,r)=>{"use strict";var n=r(1807),i=r(1483),o=r(1704),a=TypeError;e.exports=function(e,t){var r,s;if("string"===t&&i(r=e.toString)&&!o(s=n(r,e)))return s;if(i(r=e.valueOf)&&!o(s=n(r,e)))return s;if("string"!==t&&i(r=e.toString)&&!o(s=n(r,e)))return s;throw new a("Can't convert object to primitive value")}},9497:(e,t,r)=>{"use strict";var n=r(1409),i=r(4762),o=r(2278),a=r(4347),s=r(2293),c=i([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=o.f(s(e)),r=a.f;return r?c(t,r(e)):t}},6589:(e,t,r)=>{"use strict";var n=r(8389);e.exports=n},4193:e=>{"use strict";e.exports=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}}},5502:(e,t,r)=>{"use strict";var n=r(8389),i=r(2832),o=r(1483),a=r(8730),s=r(7268),c=r(1),u=r(6956),l=r(938),p=r(9557),f=r(6170),d=i&&i.prototype,v=c("species"),m=!1,y=o(n.PromiseRejectionEvent),h=a("Promise",(function(){var e=s(i),t=e!==String(i);if(!t&&66===f)return!0;if(p&&(!d.catch||!d.finally))return!0;if(!f||f<51||!/native code/.test(e)){var r=new i((function(e){e(1)})),n=function(e){e((function(){}),(function(){}))};if((r.constructor={})[v]=n,!(m=r.then((function(){}))instanceof n))return!0}return!t&&(u||l)&&!y}));e.exports={CONSTRUCTOR:h,REJECTION_EVENT:y,SUBCLASSING:m}},2832:(e,t,r)=>{"use strict";var n=r(8389);e.exports=n.Promise},2172:(e,t,r)=>{"use strict";var n=r(2293),i=r(1704),o=r(1173);e.exports=function(e,t){if(n(e),i(t)&&t.constructor===e)return t;var r=o.f(e);return(0,r.resolve)(t),r.promise}},1407:(e,t,r)=>{"use strict";var n=r(2832),i=r(1554),o=r(5502).CONSTRUCTOR;e.exports=o||!i((function(e){n.all(e).then(void 0,(function(){}))}))},7150:(e,t,r)=>{"use strict";var n=r(5835).f;e.exports=function(e,t,r){r in e||n(e,r,{configurable:!0,get:function(){return t[r]},set:function(e){t[r]=e}})}},5459:e=>{"use strict";var t=function(){this.head=null,this.tail=null};t.prototype={add:function(e){var t={item:e,next:null},r=this.tail;r?r.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return null===(this.head=e.next)&&(this.tail=null),e.item}},e.exports=t},2428:(e,t,r)=>{"use strict";var n=r(1807),i=r(2293),o=r(1483),a=r(1278),s=r(8865),c=TypeError;e.exports=function(e,t){var r=e.exec;if(o(r)){var u=n(r,e,t);return null!==u&&i(u),u}if("RegExp"===a(e))return n(s,e,t);throw new c("RegExp#exec called on incompatible receiver")}},8865:(e,t,r)=>{"use strict";var n,i,o=r(1807),a=r(4762),s=r(6261),c=r(6653),u=r(7435),l=r(7255),p=r(5290),f=r(4483).get,d=r(3933),v=r(4528),m=l("native-string-replace",String.prototype.replace),y=RegExp.prototype.exec,h=y,b=a("".charAt),g=a("".indexOf),_=a("".replace),w=a("".slice),k=(i=/b*/g,o(y,n=/a/,"a"),o(y,i,"a"),0!==n.lastIndex||0!==i.lastIndex),S=u.BROKEN_CARET,j=void 0!==/()??/.exec("")[1];(k||j||S||d||v)&&(h=function(e){var t,r,n,i,a,u,l,d=this,v=f(d),x=s(e),O=v.raw;if(O)return O.lastIndex=d.lastIndex,t=o(h,O,x),d.lastIndex=O.lastIndex,t;var E=v.groups,P=S&&d.sticky,A=o(c,d),z=d.source,C=0,T=x;if(P&&(A=_(A,"y",""),-1===g(A,"g")&&(A+="g"),T=w(x,d.lastIndex),d.lastIndex>0&&(!d.multiline||d.multiline&&"\n"!==b(x,d.lastIndex-1))&&(z="(?: "+z+")",T=" "+T,C++),r=new RegExp("^(?:"+z+")",A)),j&&(r=new RegExp("^"+z+"$(?!\\s)",A)),k&&(n=d.lastIndex),i=o(y,P?r:d,T),P?i?(i.input=w(i.input,C),i[0]=w(i[0],C),i.index=d.lastIndex,d.lastIndex+=i[0].length):d.lastIndex=0:k&&i&&(d.lastIndex=d.global?i.index+i[0].length:n),j&&i&&i.length>1&&o(m,i[0],r,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(i[a]=void 0)})),i&&E)for(i.groups=u=p(null),a=0;a<E.length;a++)u[(l=E[a])[0]]=i[l[1]];return i}),e.exports=h},6653:(e,t,r)=>{"use strict";var n=r(2293);e.exports=function(){var e=n(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t}},9736:(e,t,r)=>{"use strict";var n=r(1807),i=r(5755),o=r(4815),a=r(6653),s=RegExp.prototype;e.exports=function(e){var t=e.flags;return void 0!==t||"flags"in s||i(e,"flags")||!o(s,e)?t:n(a,e)}},7435:(e,t,r)=>{"use strict";var n=r(8473),i=r(8389).RegExp,o=n((function(){var e=i("a","y");return e.lastIndex=2,null!==e.exec("abcd")})),a=o||n((function(){return!i("a","y").sticky})),s=o||n((function(){var e=i("^r","gy");return e.lastIndex=2,null!==e.exec("str")}));e.exports={BROKEN_CARET:s,MISSED_STICKY:a,UNSUPPORTED_Y:o}},3933:(e,t,r)=>{"use strict";var n=r(8473),i=r(8389).RegExp;e.exports=n((function(){var e=i(".","s");return!(e.dotAll&&e.test("\n")&&"s"===e.flags)}))},4528:(e,t,r)=>{"use strict";var n=r(8473),i=r(8389).RegExp;e.exports=n((function(){var e=i("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")}))},3312:(e,t,r)=>{"use strict";var n=r(5983),i=TypeError;e.exports=function(e){if(n(e))throw new i("Can't call method on "+e);return e}},8123:(e,t,r)=>{"use strict";var n=r(8389),i=r(382),o=Object.getOwnPropertyDescriptor;e.exports=function(e){if(!i)return n[e];var t=o(n,e);return t&&t.value}},9570:(e,t,r)=>{"use strict";var n,i=r(8389),o=r(3067),a=r(1483),s=r(5413),c=r(9966),u=r(1698),l=r(4066),p=i.Function,f=/MSIE .\./.test(c)||s&&((n=i.Bun.version.split(".")).length<3||"0"===n[0]&&(n[1]<3||"3"===n[1]&&"0"===n[2]));e.exports=function(e,t){var r=t?2:1;return f?function(n,i){var s=l(arguments.length,1)>r,c=a(n)?n:p(n),f=s?u(arguments,r):[],d=s?function(){o(c,this,f)}:c;return t?e(d,i):e(d)}:e}},240:(e,t,r)=>{"use strict";var n=r(1409),i=r(3864),o=r(1),a=r(382),s=o("species");e.exports=function(e){var t=n(e);a&&t&&!t[s]&&i(t,s,{configurable:!0,get:function(){return this}})}},2277:(e,t,r)=>{"use strict";var n=r(5835).f,i=r(5755),o=r(1)("toStringTag");e.exports=function(e,t,r){e&&!r&&(e=e.prototype),e&&!i(e,o)&&n(e,o,{configurable:!0,value:t})}},5409:(e,t,r)=>{"use strict";var n=r(7255),i=r(1866),o=n("keys");e.exports=function(e){return o[e]||(o[e]=i(e))}},1831:(e,t,r)=>{"use strict";var n=r(9557),i=r(8389),o=r(2095),a="__core-js_shared__",s=e.exports=i[a]||o(a,{});(s.versions||(s.versions=[])).push({version:"3.36.0",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.36.0/LICENSE",source:"https://github.com/zloirock/core-js"})},7255:(e,t,r)=>{"use strict";var n=r(1831);e.exports=function(e,t){return n[e]||(n[e]=t||{})}},483:(e,t,r)=>{"use strict";var n=r(2293),i=r(2374),o=r(5983),a=r(1)("species");e.exports=function(e,t){var r,s=n(e).constructor;return void 0===s||o(r=n(s)[a])?t:i(r)}},9105:(e,t,r)=>{"use strict";var n=r(4762),i=r(3005),o=r(6261),a=r(3312),s=n("".charAt),c=n("".charCodeAt),u=n("".slice),l=function(e){return function(t,r){var n,l,p=o(a(t)),f=i(r),d=p.length;return f<0||f>=d?e?"":void 0:(n=c(p,f))<55296||n>56319||f+1===d||(l=c(p,f+1))<56320||l>57343?e?s(p,f):n:e?u(p,f,f+2):l-56320+(n-55296<<10)+65536}};e.exports={codeAt:l(!1),charAt:l(!0)}},4544:(e,t,r)=>{"use strict";var n=r(4762),i=r(3312),o=r(6261),a=r(5870),s=n("".replace),c=RegExp("^["+a+"]+"),u=RegExp("(^|[^"+a+"])["+a+"]+$"),l=function(e){return function(t){var r=o(i(t));return 1&e&&(r=s(r,c,"")),2&e&&(r=s(r,u,"$1")),r}};e.exports={start:l(1),end:l(2),trim:l(3)}},6029:(e,t,r)=>{"use strict";var n=r(6170),i=r(8473),o=r(8389).String;e.exports=!!Object.getOwnPropertySymbols&&!i((function(){var e=Symbol("symbol detection");return!o(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},8192:(e,t,r)=>{"use strict";var n=r(1807),i=r(1409),o=r(1),a=r(7914);e.exports=function(){var e=i("Symbol"),t=e&&e.prototype,r=t&&t.valueOf,s=o("toPrimitive");t&&!t[s]&&a(t,s,(function(e){return n(r,this)}),{arity:1})}},3218:(e,t,r)=>{"use strict";var n=r(6029);e.exports=n&&!!Symbol.for&&!!Symbol.keyFor},7007:(e,t,r)=>{"use strict";var n,i,o,a,s=r(8389),c=r(3067),u=r(2914),l=r(1483),p=r(5755),f=r(8473),d=r(2811),v=r(1698),m=r(3145),y=r(4066),h=r(8417),b=r(4334),g=s.setImmediate,_=s.clearImmediate,w=s.process,k=s.Dispatch,S=s.Function,j=s.MessageChannel,x=s.String,O=0,E={},P="onreadystatechange";f((function(){n=s.location}));var A=function(e){if(p(E,e)){var t=E[e];delete E[e],t()}},z=function(e){return function(){A(e)}},C=function(e){A(e.data)},T=function(e){s.postMessage(x(e),n.protocol+"//"+n.host)};g&&_||(g=function(e){y(arguments.length,1);var t=l(e)?e:S(e),r=v(arguments,1);return E[++O]=function(){c(t,void 0,r)},i(O),O},_=function(e){delete E[e]},b?i=function(e){w.nextTick(z(e))}:k&&k.now?i=function(e){k.now(z(e))}:j&&!h?(a=(o=new j).port2,o.port1.onmessage=C,i=u(a.postMessage,a)):s.addEventListener&&l(s.postMessage)&&!s.importScripts&&n&&"file:"!==n.protocol&&!f(T)?(i=T,s.addEventListener("message",C,!1)):i=P in m("script")?function(e){d.appendChild(m("script"))[P]=function(){d.removeChild(this),A(e)}}:function(e){setTimeout(z(e),0)}),e.exports={set:g,clear:_}},2430:(e,t,r)=>{"use strict";var n=r(4762);e.exports=n(1..valueOf)},3392:(e,t,r)=>{"use strict";var n=r(3005),i=Math.max,o=Math.min;e.exports=function(e,t){var r=n(e);return r<0?i(r+t,0):o(r,t)}},5599:(e,t,r)=>{"use strict";var n=r(2121),i=r(3312);e.exports=function(e){return n(i(e))}},3005:(e,t,r)=>{"use strict";var n=r(1703);e.exports=function(e){var t=+e;return t!=t||0===t?0:n(t)}},8324:(e,t,r)=>{"use strict";var n=r(3005),i=Math.min;e.exports=function(e){var t=n(e);return t>0?i(t,9007199254740991):0}},2347:(e,t,r)=>{"use strict";var n=r(3312),i=Object;e.exports=function(e){return i(n(e))}},2355:(e,t,r)=>{"use strict";var n=r(1807),i=r(1704),o=r(1423),a=r(2564),s=r(348),c=r(1),u=TypeError,l=c("toPrimitive");e.exports=function(e,t){if(!i(e)||o(e))return e;var r,c=a(e,l);if(c){if(void 0===t&&(t="default"),r=n(c,e,t),!i(r)||o(r))return r;throw new u("Can't convert object to primitive value")}return void 0===t&&(t="number"),s(e,t)}},3815:(e,t,r)=>{"use strict";var n=r(2355),i=r(1423);e.exports=function(e){var t=n(e,"string");return i(t)?t:t+""}},4338:(e,t,r)=>{"use strict";var n={};n[r(1)("toStringTag")]="z",e.exports="[object z]"===String(n)},6261:(e,t,r)=>{"use strict";var n=r(6145),i=String;e.exports=function(e){if("Symbol"===n(e))throw new TypeError("Cannot convert a Symbol value to a string");return i(e)}},8761:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},1866:(e,t,r)=>{"use strict";var n=r(4762),i=0,o=Math.random(),a=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++i+o,36)}},5022:(e,t,r)=>{"use strict";var n=r(6029);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},3896:(e,t,r)=>{"use strict";var n=r(382),i=r(8473);e.exports=n&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},4066:e=>{"use strict";var t=TypeError;e.exports=function(e,r){if(e<r)throw new t("Not enough arguments");return e}},4644:(e,t,r)=>{"use strict";var n=r(8389),i=r(1483),o=n.WeakMap;e.exports=i(o)&&/native code/.test(String(o))},7849:(e,t,r)=>{"use strict";var n=r(6589),i=r(5755),o=r(5373),a=r(5835).f;e.exports=function(e){var t=n.Symbol||(n.Symbol={});i(t,e)||a(t,e,{value:o.f(e)})}},5373:(e,t,r)=>{"use strict";var n=r(1);t.f=n},1:(e,t,r)=>{"use strict";var n=r(8389),i=r(7255),o=r(5755),a=r(1866),s=r(6029),c=r(5022),u=n.Symbol,l=i("wks"),p=c?u.for||u:u&&u.withoutSetter||a;e.exports=function(e){return o(l,e)||(l[e]=s&&o(u,e)?u[e]:p("Symbol."+e)),l[e]}},5870:e=>{"use strict";e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},4776:(e,t,r)=>{"use strict";var n=r(8612),i=r(8473),o=r(4914),a=r(1704),s=r(2347),c=r(6960),u=r(1091),l=r(670),p=r(4551),f=r(4595),d=r(1),v=r(6170),m=d("isConcatSpreadable"),y=v>=51||!i((function(){var e=[];return e[m]=!1,e.concat()[0]!==e})),h=function(e){if(!a(e))return!1;var t=e[m];return void 0!==t?!!t:o(e)};n({target:"Array",proto:!0,arity:1,forced:!y||!f("concat")},{concat:function(e){var t,r,n,i,o,a=s(this),f=p(a,0),d=0;for(t=-1,n=arguments.length;t<n;t++)if(h(o=-1===t?a:arguments[t]))for(i=c(o),u(d+i),r=0;r<i;r++,d++)r in o&&l(f,d,o[r]);else u(d+1),l(f,d++,o);return f.length=d,f}})},4382:(e,t,r)=>{"use strict";var n=r(8612),i=r(2867).filter;n({target:"Array",proto:!0,forced:!r(4595)("filter")},{filter:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},2084:(e,t,r)=>{"use strict";var n=r(8612),i=r(2867).find,o=r(7095),a="find",s=!0;a in[]&&Array(1)[a]((function(){s=!1})),n({target:"Array",proto:!0,forced:s},{find:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),o(a)},9892:(e,t,r)=>{"use strict";var n=r(8612),i=r(6142);n({target:"Array",stat:!0,forced:!r(1554)((function(e){Array.from(e)}))},{from:i})},6281:(e,t,r)=>{"use strict";var n=r(8612),i=r(6651).includes,o=r(8473),a=r(7095);n({target:"Array",proto:!0,forced:o((function(){return!Array(1).includes()}))},{includes:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),a("includes")},4962:(e,t,r)=>{"use strict";var n=r(5599),i=r(7095),o=r(6775),a=r(4483),s=r(5835).f,c=r(5662),u=r(5247),l=r(9557),p=r(382),f="Array Iterator",d=a.set,v=a.getterFor(f);e.exports=c(Array,"Array",(function(e,t){d(this,{type:f,target:n(e),index:0,kind:t})}),(function(){var e=v(this),t=e.target,r=e.index++;if(!t||r>=t.length)return e.target=void 0,u(void 0,!0);switch(e.kind){case"keys":return u(r,!1);case"values":return u(t[r],!1)}return u([r,t[r]],!1)}),"values");var m=o.Arguments=o.Array;if(i("keys"),i("values"),i("entries"),!l&&p&&"values"!==m.name)try{s(m,"name",{value:"values"})}catch(e){}},6216:(e,t,r)=>{"use strict";var n=r(8612),i=r(4762),o=r(2121),a=r(5599),s=r(3152),c=i([].join);n({target:"Array",proto:!0,forced:o!==Object||!s("join",",")},{join:function(e){return c(a(this),void 0===e?",":e)}})},6584:(e,t,r)=>{"use strict";var n=r(8612),i=r(2867).map;n({target:"Array",proto:!0,forced:!r(4595)("map")},{map:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},9336:(e,t,r)=>{"use strict";var n=r(8612),i=r(4914),o=r(943),a=r(1704),s=r(3392),c=r(6960),u=r(5599),l=r(670),p=r(1),f=r(4595),d=r(1698),v=f("slice"),m=p("species"),y=Array,h=Math.max;n({target:"Array",proto:!0,forced:!v},{slice:function(e,t){var r,n,p,f=u(this),v=c(f),b=s(e,v),g=s(void 0===t?v:t,v);if(i(f)&&(r=f.constructor,(o(r)&&(r===y||i(r.prototype))||a(r)&&null===(r=r[m]))&&(r=void 0),r===y||void 0===r))return d(f,b,g);for(n=new(void 0===r?y:r)(h(g-b,0)),p=0;b<g;b++,p++)b in f&&l(n,p,f[b]);return n.length=p,n}})},6448:(e,t,r)=>{"use strict";var n=r(8612),i=r(4762),o=r(8120),a=r(2347),s=r(6960),c=r(6060),u=r(6261),l=r(8473),p=r(7354),f=r(3152),d=r(7332),v=r(8996),m=r(6170),y=r(5158),h=[],b=i(h.sort),g=i(h.push),_=l((function(){h.sort(void 0)})),w=l((function(){h.sort(null)})),k=f("sort"),S=!l((function(){if(m)return m<70;if(!(d&&d>3)){if(v)return!0;if(y)return y<603;var e,t,r,n,i="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(n=0;n<47;n++)h.push({k:t+n,v:r})}for(h.sort((function(e,t){return t.v-e.v})),n=0;n<h.length;n++)t=h[n].k.charAt(0),i.charAt(i.length-1)!==t&&(i+=t);return"DGBEFHACIJK"!==i}}));n({target:"Array",proto:!0,forced:_||!w||!k||!S},{sort:function(e){void 0!==e&&o(e);var t=a(this);if(S)return void 0===e?b(t):b(t,e);var r,n,i=[],l=s(t);for(n=0;n<l;n++)n in t&&g(i,t[n]);for(p(i,function(e){return function(t,r){return void 0===r?-1:void 0===t?1:void 0!==e?+e(t,r)||0:u(t)>u(r)?1:-1}}(e)),r=s(i),n=0;n<r;)t[n]=i[n++];for(;n<l;)c(t,n++);return t}})},4754:(e,t,r)=>{"use strict";var n=r(5755),i=r(7914),o=r(6446),a=r(1)("toPrimitive"),s=Date.prototype;n(s,a)||i(s,a,o)},1908:(e,t,r)=>{"use strict";var n=r(382),i=r(2048).EXISTS,o=r(4762),a=r(3864),s=Function.prototype,c=o(s.toString),u=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,l=o(u.exec);n&&!i&&a(s,"name",{configurable:!0,get:function(){try{return l(u,c(this))[1]}catch(e){return""}}})},6184:(e,t,r)=>{"use strict";var n=r(8612),i=r(1409),o=r(3067),a=r(1807),s=r(4762),c=r(8473),u=r(1483),l=r(1423),p=r(1698),f=r(5215),d=r(6029),v=String,m=i("JSON","stringify"),y=s(/./.exec),h=s("".charAt),b=s("".charCodeAt),g=s("".replace),_=s(1..toString),w=/[\uD800-\uDFFF]/g,k=/^[\uD800-\uDBFF]$/,S=/^[\uDC00-\uDFFF]$/,j=!d||c((function(){var e=i("Symbol")("stringify detection");return"[null]"!==m([e])||"{}"!==m({a:e})||"{}"!==m(Object(e))})),x=c((function(){return'"\\udf06\\ud834"'!==m("\udf06\ud834")||'"\\udead"'!==m("\udead")})),O=function(e,t){var r=p(arguments),n=f(t);if(u(n)||void 0!==e&&!l(e))return r[1]=function(e,t){if(u(n)&&(t=a(n,this,v(e),t)),!l(t))return t},o(m,null,r)},E=function(e,t,r){var n=h(r,t-1),i=h(r,t+1);return y(k,e)&&!y(S,i)||y(S,e)&&!y(k,n)?"\\u"+_(b(e,0),16):e};m&&n({target:"JSON",stat:!0,arity:3,forced:j||x},{stringify:function(e,t,r){var n=p(arguments),i=o(j?O:m,null,n);return x&&"string"==typeof i?g(i,w,E):i}})},2725:(e,t,r)=>{"use strict";r(7446)("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),r(4092))},8551:(e,t,r)=>{"use strict";r(2725)},94:(e,t,r)=>{"use strict";var n=r(8612),i=r(9557),o=r(382),a=r(8389),s=r(6589),c=r(4762),u=r(8730),l=r(5755),p=r(2429),f=r(4815),d=r(1423),v=r(2355),m=r(8473),y=r(2278).f,h=r(4961).f,b=r(5835).f,g=r(2430),_=r(4544).trim,w="Number",k=a[w],S=s[w],j=k.prototype,x=a.TypeError,O=c("".slice),E=c("".charCodeAt),P=u(w,!k(" 0o1")||!k("0b1")||k("+0x1")),A=function(e){var t,r=arguments.length<1?0:k(function(e){var t=v(e,"number");return"bigint"==typeof t?t:function(e){var t,r,n,i,o,a,s,c,u=v(e,"number");if(d(u))throw new x("Cannot convert a Symbol value to a number");if("string"==typeof u&&u.length>2)if(u=_(u),43===(t=E(u,0))||45===t){if(88===(r=E(u,2))||120===r)return NaN}else if(48===t){switch(E(u,1)){case 66:case 98:n=2,i=49;break;case 79:case 111:n=8,i=55;break;default:return+u}for(a=(o=O(u,2)).length,s=0;s<a;s++)if((c=E(o,s))<48||c>i)return NaN;return parseInt(o,n)}return+u}(t)}(e));return f(j,t=this)&&m((function(){g(t)}))?p(Object(r),this,A):r};A.prototype=j,P&&!i&&(j.constructor=A),n({global:!0,constructor:!0,wrap:!0,forced:P},{Number:A});var z=function(e,t){for(var r,n=o?y(t):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),i=0;n.length>i;i++)l(t,r=n[i])&&!l(e,r)&&b(e,r,h(t,r))};i&&S&&z(s[w],S),(P||i)&&z(s[w],k)},7575:(e,t,r)=>{"use strict";var n=r(8612),i=r(1439);n({target:"Object",stat:!0,arity:2,forced:Object.assign!==i},{assign:i})},7132:(e,t,r)=>{"use strict";var n=r(8612),i=r(5627).entries;n({target:"Object",stat:!0},{entries:function(e){return i(e)}})},6457:(e,t,r)=>{"use strict";var n=r(8612),i=r(8473),o=r(5599),a=r(4961).f,s=r(382);n({target:"Object",stat:!0,forced:!s||i((function(){a(1)})),sham:!s},{getOwnPropertyDescriptor:function(e,t){return a(o(e),t)}})},8908:(e,t,r)=>{"use strict";var n=r(8612),i=r(382),o=r(9497),a=r(5599),s=r(4961),c=r(670);n({target:"Object",stat:!0,sham:!i},{getOwnPropertyDescriptors:function(e){for(var t,r,n=a(e),i=s.f,u=o(n),l={},p=0;u.length>p;)void 0!==(r=i(n,t=u[p++]))&&c(l,t,r);return l}})},7859:(e,t,r)=>{"use strict";var n=r(8612),i=r(6029),o=r(8473),a=r(4347),s=r(2347);n({target:"Object",stat:!0,forced:!i||o((function(){a.f(1)}))},{getOwnPropertySymbols:function(e){var t=a.f;return t?t(s(e)):[]}})},6437:(e,t,r)=>{"use strict";var n=r(8612),i=r(8473),o=r(2347),a=r(3181),s=r(9441);n({target:"Object",stat:!0,forced:i((function(){a(1)})),sham:!s},{getPrototypeOf:function(e){return a(o(e))}})},3810:(e,t,r)=>{"use strict";var n=r(8612),i=r(2347),o=r(3658);n({target:"Object",stat:!0,forced:r(8473)((function(){o(1)}))},{keys:function(e){return o(i(e))}})},2697:(e,t,r)=>{"use strict";r(8612)({target:"Object",stat:!0},{setPrototypeOf:r(1953)})},8557:(e,t,r)=>{"use strict";var n=r(4338),i=r(7914),o=r(5685);n||i(Object.prototype,"toString",o,{unsafe:!0})},6249:(e,t,r)=>{"use strict";var n=r(8612),i=r(1807),o=r(8120),a=r(1173),s=r(4193),c=r(1506);n({target:"Promise",stat:!0,forced:r(1407)},{all:function(e){var t=this,r=a.f(t),n=r.resolve,u=r.reject,l=s((function(){var r=o(t.resolve),a=[],s=0,l=1;c(e,(function(e){var o=s++,c=!1;l++,i(r,t,e).then((function(e){c||(c=!0,a[o]=e,--l||n(a))}),u)})),--l||n(a)}));return l.error&&u(l.value),r.promise}})},6681:(e,t,r)=>{"use strict";var n=r(8612),i=r(9557),o=r(5502).CONSTRUCTOR,a=r(2832),s=r(1409),c=r(1483),u=r(7914),l=a&&a.prototype;if(n({target:"Promise",proto:!0,forced:o,real:!0},{catch:function(e){return this.then(void 0,e)}}),!i&&c(a)){var p=s("Promise").prototype.catch;l.catch!==p&&u(l,"catch",p,{unsafe:!0})}},8786:(e,t,r)=>{"use strict";var n,i,o,a=r(8612),s=r(9557),c=r(4334),u=r(8389),l=r(1807),p=r(7914),f=r(1953),d=r(2277),v=r(240),m=r(8120),y=r(1483),h=r(1704),b=r(6021),g=r(483),_=r(7007).set,w=r(553),k=r(1339),S=r(4193),j=r(5459),x=r(4483),O=r(2832),E=r(5502),P=r(1173),A="Promise",z=E.CONSTRUCTOR,C=E.REJECTION_EVENT,T=E.SUBCLASSING,N=x.getterFor(A),I=x.set,D=O&&O.prototype,R=O,M=D,q=u.TypeError,U=u.document,L=u.process,F=P.f,B=F,H=!!(U&&U.createEvent&&u.dispatchEvent),V="unhandledrejection",W=function(e){var t;return!(!h(e)||!y(t=e.then))&&t},K=function(e,t){var r,n,i,o=t.value,a=1===t.state,s=a?e.ok:e.fail,c=e.resolve,u=e.reject,p=e.domain;try{s?(a||(2===t.rejection&&J(t),t.rejection=1),!0===s?r=o:(p&&p.enter(),r=s(o),p&&(p.exit(),i=!0)),r===e.promise?u(new q("Promise-chain cycle")):(n=W(r))?l(n,r,c,u):c(r)):u(o)}catch(e){p&&!i&&p.exit(),u(e)}},$=function(e,t){e.notified||(e.notified=!0,w((function(){for(var r,n=e.reactions;r=n.get();)K(r,e);e.notified=!1,t&&!e.rejection&&Z(e)})))},G=function(e,t,r){var n,i;H?((n=U.createEvent("Event")).promise=t,n.reason=r,n.initEvent(e,!1,!0),u.dispatchEvent(n)):n={promise:t,reason:r},!C&&(i=u["on"+e])?i(n):e===V&&k("Unhandled promise rejection",r)},Z=function(e){l(_,u,(function(){var t,r=e.facade,n=e.value;if(Y(e)&&(t=S((function(){c?L.emit("unhandledRejection",n,r):G(V,r,n)})),e.rejection=c||Y(e)?2:1,t.error))throw t.value}))},Y=function(e){return 1!==e.rejection&&!e.parent},J=function(e){l(_,u,(function(){var t=e.facade;c?L.emit("rejectionHandled",t):G("rejectionhandled",t,e.value)}))},X=function(e,t,r){return function(n){e(t,n,r)}},Q=function(e,t,r){e.done||(e.done=!0,r&&(e=r),e.value=t,e.state=2,$(e,!0))},ee=function(e,t,r){if(!e.done){e.done=!0,r&&(e=r);try{if(e.facade===t)throw new q("Promise can't be resolved itself");var n=W(t);n?w((function(){var r={done:!1};try{l(n,t,X(ee,r,e),X(Q,r,e))}catch(t){Q(r,t,e)}})):(e.value=t,e.state=1,$(e,!1))}catch(t){Q({done:!1},t,e)}}};if(z&&(M=(R=function(e){b(this,M),m(e),l(n,this);var t=N(this);try{e(X(ee,t),X(Q,t))}catch(e){Q(t,e)}}).prototype,(n=function(e){I(this,{type:A,done:!1,notified:!1,parent:!1,reactions:new j,rejection:!1,state:0,value:void 0})}).prototype=p(M,"then",(function(e,t){var r=N(this),n=F(g(this,R));return r.parent=!0,n.ok=!y(e)||e,n.fail=y(t)&&t,n.domain=c?L.domain:void 0,0===r.state?r.reactions.add(n):w((function(){K(n,r)})),n.promise})),i=function(){var e=new n,t=N(e);this.promise=e,this.resolve=X(ee,t),this.reject=X(Q,t)},P.f=F=function(e){return e===R||void 0===e?new i(e):B(e)},!s&&y(O)&&D!==Object.prototype)){o=D.then,T||p(D,"then",(function(e,t){var r=this;return new R((function(e,t){l(o,r,e,t)})).then(e,t)}),{unsafe:!0});try{delete D.constructor}catch(e){}f&&f(D,M)}a({global:!0,constructor:!0,wrap:!0,forced:z},{Promise:R}),d(R,A,!1,!0),v(A)},76:(e,t,r)=>{"use strict";r(8786),r(6249),r(6681),r(1681),r(9231),r(5774)},1681:(e,t,r)=>{"use strict";var n=r(8612),i=r(1807),o=r(8120),a=r(1173),s=r(4193),c=r(1506);n({target:"Promise",stat:!0,forced:r(1407)},{race:function(e){var t=this,r=a.f(t),n=r.reject,u=s((function(){var a=o(t.resolve);c(e,(function(e){i(a,t,e).then(r.resolve,n)}))}));return u.error&&n(u.value),r.promise}})},9231:(e,t,r)=>{"use strict";var n=r(8612),i=r(1173);n({target:"Promise",stat:!0,forced:r(5502).CONSTRUCTOR},{reject:function(e){var t=i.f(this);return(0,t.reject)(e),t.promise}})},5774:(e,t,r)=>{"use strict";var n=r(8612),i=r(1409),o=r(9557),a=r(2832),s=r(5502).CONSTRUCTOR,c=r(2172),u=i("Promise"),l=o&&!s;n({target:"Promise",stat:!0,forced:o||s},{resolve:function(e){return c(l&&this===u?a:this,e)}})},1359:(e,t,r)=>{"use strict";var n=r(8612),i=r(1409),o=r(3067),a=r(2164),s=r(2374),c=r(2293),u=r(1704),l=r(5290),p=r(8473),f=i("Reflect","construct"),d=Object.prototype,v=[].push,m=p((function(){function e(){}return!(f((function(){}),[],e)instanceof e)})),y=!p((function(){f((function(){}))})),h=m||y;n({target:"Reflect",stat:!0,forced:h,sham:h},{construct:function(e,t){s(e),c(t);var r=arguments.length<3?e:s(arguments[2]);if(y&&!m)return f(e,t,r);if(e===r){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var n=[null];return o(v,n,t),new(o(a,e,n))}var i=r.prototype,p=l(u(i)?i:d),h=o(e,p,t);return u(h)?h:p}})},646:(e,t,r)=>{"use strict";var n=r(382),i=r(8389),o=r(4762),a=r(8730),s=r(2429),c=r(9037),u=r(5290),l=r(2278).f,p=r(4815),f=r(4786),d=r(6261),v=r(9736),m=r(7435),y=r(7150),h=r(7914),b=r(8473),g=r(5755),_=r(4483).enforce,w=r(240),k=r(1),S=r(3933),j=r(4528),x=k("match"),O=i.RegExp,E=O.prototype,P=i.SyntaxError,A=o(E.exec),z=o("".charAt),C=o("".replace),T=o("".indexOf),N=o("".slice),I=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,D=/a/g,R=/a/g,M=new O(D)!==D,q=m.MISSED_STICKY,U=m.UNSUPPORTED_Y;if(a("RegExp",n&&(!M||q||S||j||b((function(){return R[x]=!1,O(D)!==D||O(R)===R||"/a/i"!==String(O(D,"i"))}))))){for(var L=function(e,t){var r,n,i,o,a,l,m=p(E,this),y=f(e),h=void 0===t,b=[],w=e;if(!m&&y&&h&&e.constructor===L)return e;if((y||p(E,e))&&(e=e.source,h&&(t=v(w))),e=void 0===e?"":d(e),t=void 0===t?"":d(t),w=e,S&&"dotAll"in D&&(n=!!t&&T(t,"s")>-1)&&(t=C(t,/s/g,"")),r=t,q&&"sticky"in D&&(i=!!t&&T(t,"y")>-1)&&U&&(t=C(t,/y/g,"")),j&&(o=function(e){for(var t,r=e.length,n=0,i="",o=[],a=u(null),s=!1,c=!1,l=0,p="";n<=r;n++){if("\\"===(t=z(e,n)))t+=z(e,++n);else if("]"===t)s=!1;else if(!s)switch(!0){case"["===t:s=!0;break;case"("===t:A(I,N(e,n+1))&&(n+=2,c=!0),i+=t,l++;continue;case">"===t&&c:if(""===p||g(a,p))throw new P("Invalid capture group name");a[p]=!0,o[o.length]=[p,l],c=!1,p="";continue}c?p+=t:i+=t}return[i,o]}(e),e=o[0],b=o[1]),a=s(O(e,t),m?this:E,L),(n||i||b.length)&&(l=_(a),n&&(l.dotAll=!0,l.raw=L(function(e){for(var t,r=e.length,n=0,i="",o=!1;n<=r;n++)"\\"!==(t=z(e,n))?o||"."!==t?("["===t?o=!0:"]"===t&&(o=!1),i+=t):i+="[\\s\\S]":i+=t+z(e,++n);return i}(e),r)),i&&(l.sticky=!0),b.length&&(l.groups=b)),e!==w)try{c(a,"source",""===w?"(?:)":w)}catch(e){}return a},F=l(O),B=0;F.length>B;)y(L,O,F[B++]);E.constructor=L,L.prototype=E,h(i,"RegExp",L,{constructor:!0})}w("RegExp")},5021:(e,t,r)=>{"use strict";var n=r(8612),i=r(8865);n({target:"RegExp",proto:!0,forced:/./.exec!==i},{exec:i})},3687:(e,t,r)=>{"use strict";var n=r(2048).PROPER,i=r(7914),o=r(2293),a=r(6261),s=r(8473),c=r(9736),u="toString",l=RegExp.prototype,p=l[u],f=s((function(){return"/a/b"!==p.call({source:"a",flags:"b"})})),d=n&&p.name!==u;(f||d)&&i(l,u,(function(){var e=o(this);return"/"+a(e.source)+"/"+a(c(e))}),{unsafe:!0})},9203:(e,t,r)=>{"use strict";r(7446)("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),r(4092))},2745:(e,t,r)=>{"use strict";r(9203)},987:(e,t,r)=>{"use strict";var n,i=r(8612),o=r(3786),a=r(4961).f,s=r(8324),c=r(6261),u=r(4989),l=r(3312),p=r(4522),f=r(9557),d=o("".slice),v=Math.min,m=p("endsWith");i({target:"String",proto:!0,forced:!(!f&&!m&&(n=a(String.prototype,"endsWith"),n&&!n.writable)||m)},{endsWith:function(e){var t=c(l(this));u(e);var r=arguments.length>1?arguments[1]:void 0,n=t.length,i=void 0===r?n:v(s(r),n),o=c(e);return d(t,i-o.length,i)===o}})},9425:(e,t,r)=>{"use strict";var n=r(8612),i=r(4762),o=r(4989),a=r(3312),s=r(6261),c=r(4522),u=i("".indexOf);n({target:"String",proto:!0,forced:!c("includes")},{includes:function(e){return!!~u(s(a(this)),s(o(e)),arguments.length>1?arguments[1]:void 0)}})},3994:(e,t,r)=>{"use strict";var n=r(9105).charAt,i=r(6261),o=r(4483),a=r(5662),s=r(5247),c="String Iterator",u=o.set,l=o.getterFor(c);a(String,"String",(function(e){u(this,{type:c,string:i(e),index:0})}),(function(){var e,t=l(this),r=t.string,i=t.index;return i>=r.length?s(void 0,!0):(e=n(r,i),t.index+=e.length,s(e,!1))}))},3819:(e,t,r)=>{"use strict";var n=r(1807),i=r(3358),o=r(2293),a=r(5983),s=r(8324),c=r(6261),u=r(3312),l=r(2564),p=r(4419),f=r(2428);i("match",(function(e,t,r){return[function(t){var r=u(this),i=a(t)?void 0:l(t,e);return i?n(i,t,r):new RegExp(t)[e](c(r))},function(e){var n=o(this),i=c(e),a=r(t,n,i);if(a.done)return a.value;if(!n.global)return f(n,i);var u=n.unicode;n.lastIndex=0;for(var l,d=[],v=0;null!==(l=f(n,i));){var m=c(l[0]);d[v]=m,""===m&&(n.lastIndex=p(i,s(n.lastIndex),u)),v++}return 0===v?null:d}]}))},3062:(e,t,r)=>{"use strict";var n=r(3067),i=r(1807),o=r(4762),a=r(3358),s=r(8473),c=r(2293),u=r(1483),l=r(5983),p=r(3005),f=r(8324),d=r(6261),v=r(3312),m=r(4419),y=r(2564),h=r(708),b=r(2428),g=r(1)("replace"),_=Math.max,w=Math.min,k=o([].concat),S=o([].push),j=o("".indexOf),x=o("".slice),O="$0"==="a".replace(/./,"$0"),E=!!/./[g]&&""===/./[g]("a","$0");a("replace",(function(e,t,r){var o=E?"$":"$0";return[function(e,r){var n=v(this),o=l(e)?void 0:y(e,g);return o?i(o,e,n,r):i(t,d(n),e,r)},function(e,i){var a=c(this),s=d(e);if("string"==typeof i&&-1===j(i,o)&&-1===j(i,"$<")){var l=r(t,a,s,i);if(l.done)return l.value}var v=u(i);v||(i=d(i));var y,g=a.global;g&&(y=a.unicode,a.lastIndex=0);for(var O,E=[];null!==(O=b(a,s))&&(S(E,O),g);)""===d(O[0])&&(a.lastIndex=m(s,f(a.lastIndex),y));for(var P,A="",z=0,C=0;C<E.length;C++){for(var T,N=d((O=E[C])[0]),I=_(w(p(O.index),s.length),0),D=[],R=1;R<O.length;R++)S(D,void 0===(P=O[R])?P:String(P));var M=O.groups;if(v){var q=k([N],D,I,s);void 0!==M&&S(q,M),T=d(n(i,void 0,q))}else T=h(N,s,I,D,M,i);I>=z&&(A+=x(s,z,I)+T,z=I+N.length)}return A+x(s,z)}]}),!!s((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}))||!O||E)},4062:(e,t,r)=>{"use strict";var n,i=r(8612),o=r(3786),a=r(4961).f,s=r(8324),c=r(6261),u=r(4989),l=r(3312),p=r(4522),f=r(9557),d=o("".slice),v=Math.min,m=p("startsWith");i({target:"String",proto:!0,forced:!(!f&&!m&&(n=a(String.prototype,"startsWith"),n&&!n.writable)||m)},{startsWith:function(e){var t=c(l(this));u(e);var r=s(v(arguments.length>1?arguments[1]:void 0,t.length)),n=c(e);return d(t,r,r+n.length)===n}})},5443:(e,t,r)=>{"use strict";var n=r(8612),i=r(8389),o=r(1807),a=r(4762),s=r(9557),c=r(382),u=r(6029),l=r(8473),p=r(5755),f=r(4815),d=r(2293),v=r(5599),m=r(3815),y=r(6261),h=r(7738),b=r(5290),g=r(3658),_=r(2278),w=r(2020),k=r(4347),S=r(4961),j=r(5835),x=r(5799),O=r(7611),E=r(7914),P=r(3864),A=r(7255),z=r(5409),C=r(1507),T=r(1866),N=r(1),I=r(5373),D=r(7849),R=r(8192),M=r(2277),q=r(4483),U=r(2867).forEach,L=z("hidden"),F="Symbol",B="prototype",H=q.set,V=q.getterFor(F),W=Object[B],K=i.Symbol,$=K&&K[B],G=i.RangeError,Z=i.TypeError,Y=i.QObject,J=S.f,X=j.f,Q=w.f,ee=O.f,te=a([].push),re=A("symbols"),ne=A("op-symbols"),ie=A("wks"),oe=!Y||!Y[B]||!Y[B].findChild,ae=function(e,t,r){var n=J(W,t);n&&delete W[t],X(e,t,r),n&&e!==W&&X(W,t,n)},se=c&&l((function(){return 7!==b(X({},"a",{get:function(){return X(this,"a",{value:7}).a}})).a}))?ae:X,ce=function(e,t){var r=re[e]=b($);return H(r,{type:F,tag:e,description:t}),c||(r.description=t),r},ue=function(e,t,r){e===W&&ue(ne,t,r),d(e);var n=m(t);return d(r),p(re,n)?(r.enumerable?(p(e,L)&&e[L][n]&&(e[L][n]=!1),r=b(r,{enumerable:h(0,!1)})):(p(e,L)||X(e,L,h(1,b(null))),e[L][n]=!0),se(e,n,r)):X(e,n,r)},le=function(e,t){d(e);var r=v(t),n=g(r).concat(ve(r));return U(n,(function(t){c&&!o(pe,r,t)||ue(e,t,r[t])})),e},pe=function(e){var t=m(e),r=o(ee,this,t);return!(this===W&&p(re,t)&&!p(ne,t))&&(!(r||!p(this,t)||!p(re,t)||p(this,L)&&this[L][t])||r)},fe=function(e,t){var r=v(e),n=m(t);if(r!==W||!p(re,n)||p(ne,n)){var i=J(r,n);return!i||!p(re,n)||p(r,L)&&r[L][n]||(i.enumerable=!0),i}},de=function(e){var t=Q(v(e)),r=[];return U(t,(function(e){p(re,e)||p(C,e)||te(r,e)})),r},ve=function(e){var t=e===W,r=Q(t?ne:v(e)),n=[];return U(r,(function(e){!p(re,e)||t&&!p(W,e)||te(n,re[e])})),n};u||(E($=(K=function(){if(f($,this))throw new Z("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?y(arguments[0]):void 0,t=T(e),r=function(e){var n=void 0===this?i:this;n===W&&o(r,ne,e),p(n,L)&&p(n[L],t)&&(n[L][t]=!1);var a=h(1,e);try{se(n,t,a)}catch(e){if(!(e instanceof G))throw e;ae(n,t,a)}};return c&&oe&&se(W,t,{configurable:!0,set:r}),ce(t,e)})[B],"toString",(function(){return V(this).tag})),E(K,"withoutSetter",(function(e){return ce(T(e),e)})),O.f=pe,j.f=ue,x.f=le,S.f=fe,_.f=w.f=de,k.f=ve,I.f=function(e){return ce(N(e),e)},c&&(P($,"description",{configurable:!0,get:function(){return V(this).description}}),s||E(W,"propertyIsEnumerable",pe,{unsafe:!0}))),n({global:!0,constructor:!0,wrap:!0,forced:!u,sham:!u},{Symbol:K}),U(g(ie),(function(e){D(e)})),n({target:F,stat:!0,forced:!u},{useSetter:function(){oe=!0},useSimple:function(){oe=!1}}),n({target:"Object",stat:!0,forced:!u,sham:!c},{create:function(e,t){return void 0===t?b(e):le(b(e),t)},defineProperty:ue,defineProperties:le,getOwnPropertyDescriptor:fe}),n({target:"Object",stat:!0,forced:!u},{getOwnPropertyNames:de}),R(),M(K,F),C[L]=!0},2733:(e,t,r)=>{"use strict";var n=r(8612),i=r(382),o=r(8389),a=r(4762),s=r(5755),c=r(1483),u=r(4815),l=r(6261),p=r(3864),f=r(6726),d=o.Symbol,v=d&&d.prototype;if(i&&c(d)&&(!("description"in v)||void 0!==d().description)){var m={},y=function(){var e=arguments.length<1||void 0===arguments[0]?void 0:l(arguments[0]),t=u(v,this)?new d(e):void 0===e?d():d(e);return""===e&&(m[t]=!0),t};f(y,d),y.prototype=v,v.constructor=y;var h="Symbol(description detection)"===String(d("description detection")),b=a(v.valueOf),g=a(v.toString),_=/^Symbol\((.*)\)[^)]+$/,w=a("".replace),k=a("".slice);p(v,"description",{configurable:!0,get:function(){var e=b(this);if(s(m,e))return"";var t=g(e),r=h?k(t,7,-1):w(t,_,"$1");return""===r?void 0:r}}),n({global:!0,constructor:!0,forced:!0},{Symbol:y})}},2484:(e,t,r)=>{"use strict";var n=r(8612),i=r(1409),o=r(5755),a=r(6261),s=r(7255),c=r(3218),u=s("string-to-symbol-registry"),l=s("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!c},{for:function(e){var t=a(e);if(o(u,t))return u[t];var r=i("Symbol")(t);return u[t]=r,l[r]=t,r}})},4701:(e,t,r)=>{"use strict";r(7849)("iterator")},9305:(e,t,r)=>{"use strict";r(5443),r(2484),r(1894),r(6184),r(7859)},1894:(e,t,r)=>{"use strict";var n=r(8612),i=r(5755),o=r(1423),a=r(8761),s=r(7255),c=r(3218),u=s("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!c},{keyFor:function(e){if(!o(e))throw new TypeError(a(e)+" is not a symbol");if(i(u,e))return u[e]}})},1678:(e,t,r)=>{"use strict";var n=r(7849),i=r(8192);n("toPrimitive"),i()},3630:(e,t,r)=>{"use strict";var n=r(8389),i=r(4842),o=r(1902),a=r(4793),s=r(9037),c=function(e){if(e&&e.forEach!==a)try{s(e,"forEach",a)}catch(t){e.forEach=a}};for(var u in i)i[u]&&c(n[u]&&n[u].prototype);c(o)},2367:(e,t,r)=>{"use strict";var n=r(8389),i=r(4842),o=r(1902),a=r(4962),s=r(9037),c=r(2277),u=r(1)("iterator"),l=a.values,p=function(e,t){if(e){if(e[u]!==l)try{s(e,u,l)}catch(t){e[u]=l}if(c(e,t,!0),i[t])for(var r in a)if(e[r]!==a[r])try{s(e,r,a[r])}catch(t){e[r]=a[r]}}};for(var f in i)p(n[f]&&n[f].prototype,f);p(o,"DOMTokenList")},9833:(e,t,r)=>{"use strict";var n=r(8612),i=r(8389),o=r(9570)(i.setInterval,!0);n({global:!0,bind:!0,forced:i.setInterval!==o},{setInterval:o})},3989:(e,t,r)=>{"use strict";var n=r(8612),i=r(8389),o=r(9570)(i.setTimeout,!0);n({global:!0,bind:!0,forced:i.setTimeout!==o},{setTimeout:o})},7089:(e,t,r)=>{"use strict";r(9833),r(3989)}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var o=t[n]={exports:{}};return e[n].call(o.exports,o,o.exports,r),o.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{"use strict";r.r(n),r.d(n,{addEventListener:()=>rn,defaultConfig:()=>Hr,defaultTranslations:()=>Jr,getConfigTranslations:()=>on,getElement:()=>tn,getElementID:()=>en,getManager:()=>mn,language:()=>st,render:()=>sn,renderContextualConsentNotices:()=>cn,resetManagers:()=>vn,setup:()=>pn,show:()=>fn,updateConfig:()=>ar,validateConfig:()=>ln,version:()=>yn}),r(9305),r(2733),r(4701),r(1678),r(4776),r(4382),r(2084),r(9892),r(4962),r(6584),r(9336),r(4754),r(1908),r(8551),r(94),r(6457),r(8908),r(3810),r(8557),r(5021),r(3687),r(3994),r(3630),r(2367);var e,t,i,o,a,s,c,u,l={},p=[],f=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,d=Array.isArray;function v(e,t){for(var r in t)e[r]=t[r];return e}function m(e){var t=e.parentNode;t&&t.removeChild(e)}function y(t,r,n){var i,o,a,s={};for(a in r)"key"==a?i=r[a]:"ref"==a?o=r[a]:s[a]=r[a];if(arguments.length>2&&(s.children=arguments.length>3?e.call(arguments,2):n),"function"==typeof t&&null!=t.defaultProps)for(a in t.defaultProps)void 0===s[a]&&(s[a]=t.defaultProps[a]);return h(t,s,i,o,null)}function h(e,r,n,o,a){var s={type:e,props:r,key:n,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==a?++i:a,__i:-1,__u:0};return null==a&&null!=t.vnode&&t.vnode(s),s}function b(e){return e.children}function g(e,t){this.props=e,this.context=t}function _(e,t){if(null==t)return e.__?_(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?_(e):null}function w(e,r,n){var i,o=e.__v,a=o.__e,s=e.__P;if(s)return(i=v({},o)).__v=o.__v+1,t.vnode&&t.vnode(i),I(s,i,o,e.__n,void 0!==s.ownerSVGElement,32&o.__u?[a]:null,r,null==a?_(o):a,!!(32&o.__u),n),i.__v=o.__v,i.__.__k[i.__i]=i,i.__d=void 0,i.__e!=a&&k(i),i}function k(e){var t,r;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e){e.__e=e.__c.base=r.__e;break}return k(e)}}function S(e){(!e.__d&&(e.__d=!0)&&o.push(e)&&!j.__r++||a!==t.debounceRendering)&&((a=t.debounceRendering)||s)(j)}function j(){var e,r,n,i=[],a=[];for(o.sort(c);e=o.shift();)e.__d&&(n=o.length,r=w(e,i,a)||r,0===n||o.length>n?(D(i,r,a),a.length=i.length=0,r=void 0,o.sort(c)):r&&t.__c&&t.__c(r,p));r&&D(i,r,a),j.__r=0}function x(e,t,r,n,i,o,a,s,c,u,f){var d,v,m,y,h,b=n&&n.__k||p,g=t.length;for(r.__d=c,O(r,t,b),c=r.__d,d=0;d<g;d++)null!=(m=r.__k[d])&&"boolean"!=typeof m&&"function"!=typeof m&&(v=-1===m.__i?l:b[m.__i]||l,m.__i=d,I(e,m,v,i,o,a,s,c,u,f),y=m.__e,m.ref&&v.ref!=m.ref&&(v.ref&&M(v.ref,null,m),f.push(m.ref,m.__c||y,m)),null==h&&null!=y&&(h=y),65536&m.__u||v.__k===m.__k?c=E(m,c,e):"function"==typeof m.type&&void 0!==m.__d?c=m.__d:y&&(c=y.nextSibling),m.__d=void 0,m.__u&=-196609);r.__d=c,r.__e=h}function O(e,t,r){var n,i,o,a,s,c=t.length,u=r.length,l=u,p=0;for(e.__k=[],n=0;n<c;n++)a=n+p,null!=(i=e.__k[n]=null==(i=t[n])||"boolean"==typeof i||"function"==typeof i?null:"string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?h(null,i,null,null,null):d(i)?h(b,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?h(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i)?(i.__=e,i.__b=e.__b+1,s=A(i,r,a,l),i.__i=s,o=null,-1!==s&&(l--,(o=r[s])&&(o.__u|=131072)),null==o||null===o.__v?(-1==s&&p--,"function"!=typeof i.type&&(i.__u|=65536)):s!==a&&(s===a+1?p++:s>a?l>c-a?p+=s-a:p--:s<a?s==a-1&&(p=s-a):p=0,s!==n+p&&(i.__u|=65536))):(o=r[a])&&null==o.key&&o.__e&&0==(131072&o.__u)&&(o.__e==e.__d&&(e.__d=_(o)),q(o,o,!1),r[a]=null,l--);if(l)for(n=0;n<u;n++)null!=(o=r[n])&&0==(131072&o.__u)&&(o.__e==e.__d&&(e.__d=_(o)),q(o,o))}function E(e,t,r){var n,i;if("function"==typeof e.type){for(n=e.__k,i=0;n&&i<n.length;i++)n[i]&&(n[i].__=e,t=E(n[i],t,r));return t}e.__e!=t&&(r.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8===t.nodeType);return t}function P(e,t){return t=t||[],null==e||"boolean"==typeof e||(d(e)?e.some((function(e){P(e,t)})):t.push(e)),t}function A(e,t,r,n){var i=e.key,o=e.type,a=r-1,s=r+1,c=t[r];if(null===c||c&&i==c.key&&o===c.type&&0==(131072&c.__u))return r;if(n>(null!=c&&0==(131072&c.__u)?1:0))for(;a>=0||s<t.length;){if(a>=0){if((c=t[a])&&0==(131072&c.__u)&&i==c.key&&o===c.type)return a;a--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&i==c.key&&o===c.type)return s;s++}}return-1}function z(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||f.test(t)?r:r+"px"}function C(e,t,r,n,i){var o;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||z(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||z(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])o=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=r,r?n?r.u=n.u:(r.u=Date.now(),e.addEventListener(t,o?N:T,o)):e.removeEventListener(t,o?N:T,o);else{if(i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==t&&"height"!==t&&"href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&"rowSpan"!==t&&"colSpan"!==t&&"role"!==t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,r))}}function T(e){if(this.l){var r=this.l[e.type+!1];if(e.t){if(e.t<=r.u)return}else e.t=Date.now();return r(t.event?t.event(e):e)}}function N(e){if(this.l)return this.l[e.type+!0](t.event?t.event(e):e)}function I(e,r,n,i,o,a,s,c,u,l){var p,f,m,y,h,_,w,k,S,j,O,E,P,A,z,C=r.type;if(void 0!==r.constructor)return null;128&n.__u&&(u=!!(32&n.__u),a=[c=r.__e=n.__e]),(p=t.__b)&&p(r);e:if("function"==typeof C)try{if(k=r.props,S=(p=C.contextType)&&i[p.__c],j=p?S?S.props.value:p.__:i,n.__c?w=(f=r.__c=n.__c).__=f.__E:("prototype"in C&&C.prototype.render?r.__c=f=new C(k,j):(r.__c=f=new g(k,j),f.constructor=C,f.render=U),S&&S.sub(f),f.props=k,f.state||(f.state={}),f.context=j,f.__n=i,m=f.__d=!0,f.__h=[],f._sb=[]),null==f.__s&&(f.__s=f.state),null!=C.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=v({},f.__s)),v(f.__s,C.getDerivedStateFromProps(k,f.__s))),y=f.props,h=f.state,f.__v=r,m)null==C.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(null==C.getDerivedStateFromProps&&k!==y&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(k,j),!f.__e&&(null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(k,f.__s,j)||r.__v===n.__v)){for(r.__v!==n.__v&&(f.props=k,f.state=f.__s,f.__d=!1),r.__e=n.__e,r.__k=n.__k,r.__k.forEach((function(e){e&&(e.__=r)})),O=0;O<f._sb.length;O++)f.__h.push(f._sb[O]);f._sb=[],f.__h.length&&s.push(f);break e}null!=f.componentWillUpdate&&f.componentWillUpdate(k,f.__s,j),null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(y,h,_)}))}if(f.context=j,f.props=k,f.__P=e,f.__e=!1,E=t.__r,P=0,"prototype"in C&&C.prototype.render){for(f.state=f.__s,f.__d=!1,E&&E(r),p=f.render(f.props,f.state,f.context),A=0;A<f._sb.length;A++)f.__h.push(f._sb[A]);f._sb=[]}else do{f.__d=!1,E&&E(r),p=f.render(f.props,f.state,f.context),f.state=f.__s}while(f.__d&&++P<25);f.state=f.__s,null!=f.getChildContext&&(i=v(v({},i),f.getChildContext())),m||null==f.getSnapshotBeforeUpdate||(_=f.getSnapshotBeforeUpdate(y,h)),x(e,d(z=null!=p&&p.type===b&&null==p.key?p.props.children:p)?z:[z],r,n,i,o,a,s,c,u,l),f.base=r.__e,r.__u&=-161,f.__h.length&&s.push(f),w&&(f.__E=f.__=null)}catch(e){r.__v=null,u||null!=a?(r.__e=c,r.__u|=u?160:32,a[a.indexOf(c)]=null):(r.__e=n.__e,r.__k=n.__k),t.__e(e,r,n)}else null==a&&r.__v===n.__v?(r.__k=n.__k,r.__e=n.__e):r.__e=R(n.__e,r,n,i,o,a,s,u,l);(p=t.diffed)&&p(r)}function D(e,r,n){for(var i=0;i<n.length;i++)M(n[i],n[++i],n[++i]);t.__c&&t.__c(r,e),e.some((function(r){try{e=r.__h,r.__h=[],e.some((function(e){e.call(r)}))}catch(e){t.__e(e,r.__v)}}))}function R(t,r,n,i,o,a,s,c,u){var p,f,v,y,h,b,g,w=n.props,k=r.props,S=r.type;if("svg"===S&&(o=!0),null!=a)for(p=0;p<a.length;p++)if((h=a[p])&&"setAttribute"in h==!!S&&(S?h.localName===S:3===h.nodeType)){t=h,a[p]=null;break}if(null==t){if(null===S)return document.createTextNode(k);t=o?document.createElementNS("http://www.w3.org/2000/svg",S):document.createElement(S,k.is&&k),a=null,c=!1}if(null===S)w===k||c&&t.data===k||(t.data=k);else{if(a=a&&e.call(t.childNodes),w=n.props||l,!c&&null!=a)for(w={},p=0;p<t.attributes.length;p++)w[(h=t.attributes[p]).name]=h.value;for(p in w)h=w[p],"children"==p||("dangerouslySetInnerHTML"==p?v=h:"key"===p||p in k||C(t,p,null,h,o));for(p in k)h=k[p],"children"==p?y=h:"dangerouslySetInnerHTML"==p?f=h:"value"==p?b=h:"checked"==p?g=h:"key"===p||c&&"function"!=typeof h||w[p]===h||C(t,p,h,w[p],o);if(f)c||v&&(f.__html===v.__html||f.__html===t.innerHTML)||(t.innerHTML=f.__html),r.__k=[];else if(v&&(t.innerHTML=""),x(t,d(y)?y:[y],r,n,i,o&&"foreignObject"!==S,a,s,a?a[0]:n.__k&&_(n,0),c,u),null!=a)for(p=a.length;p--;)null!=a[p]&&m(a[p]);c||(p="value",void 0!==b&&(b!==t[p]||"progress"===S&&!b||"option"===S&&b!==w[p])&&C(t,p,b,w[p],!1),p="checked",void 0!==g&&g!==t[p]&&C(t,p,g,w[p],!1))}return t}function M(e,r,n){try{"function"==typeof e?e(r):e.current=r}catch(e){t.__e(e,n)}}function q(e,r,n){var i,o;if(t.unmount&&t.unmount(e),(i=e.ref)&&(i.current&&i.current!==e.__e||M(i,null,r)),null!=(i=e.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){t.__e(e,r)}i.base=i.__P=null,e.__c=void 0}if(i=e.__k)for(o=0;o<i.length;o++)i[o]&&q(i[o],r,n||"function"!=typeof e.type);n||null==e.__e||m(e.__e),e.__=e.__e=e.__d=void 0}function U(e,t,r){return this.constructor(e,r)}function L(r,n,i){var o,a,s,c;t.__&&t.__(r,n),a=(o="function"==typeof i)?null:i&&i.__k||n.__k,s=[],c=[],I(n,r=(!o&&i||n).__k=y(b,null,[r]),a||l,l,void 0!==n.ownerSVGElement,!o&&i?[i]:a?null:n.firstChild?e.call(n.childNodes):null,s,!o&&i?i:a?a.__e:n.firstChild,o,c),r.__d=void 0,D(s,r,c)}function F(e,t){L(e,t,F)}function B(t,r,n){var i,o,a,s,c=v({},t.props);for(a in t.type&&t.type.defaultProps&&(s=t.type.defaultProps),r)"key"==a?i=r[a]:"ref"==a?o=r[a]:c[a]=void 0===r[a]&&void 0!==s?s[a]:r[a];return arguments.length>2&&(c.children=arguments.length>3?e.call(arguments,2):n),h(t.type,c,i||t.key,o||t.ref,null)}e=p.slice,t={__e:function(e,t,r,n){for(var i,o,a;t=t.__;)if((i=t.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(e)),a=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,n||{}),a=i.__d),a)return i.__E=i}catch(t){e=t}throw e}},i=0,g.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=v({},this.state),"function"==typeof e&&(e=e(v({},r),this.props)),e&&v(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),S(this))},g.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),S(this))},g.prototype.render=b,o=[],s="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,c=function(e,t){return e.__v.__b-t.__v.__b},j.__r=0,u=0;var H,V,W,K,$=0,G=[],Z=[],Y=t,J=Y.__b,X=Y.__r,Q=Y.diffed,ee=Y.__c,te=Y.unmount,re=Y.__;function ne(e,t){Y.__h&&Y.__h(V,e,$||t),$=0;var r=V.__H||(V.__H={__:[],__h:[]});return e>=r.__.length&&r.__.push({__V:Z}),r.__[e]}function ie(e){return $=1,oe(me,e)}function oe(e,t,r){var n=ne(H++,2);if(n.t=e,!n.__c&&(n.__=[r?r(t):me(void 0,t),function(e){var t=n.__N?n.__N[0]:n.__[0],r=n.t(t,e);t!==r&&(n.__N=[r,n.__[1]],n.__c.setState({}))}],n.__c=V,!V.u)){var i=function(e,t,r){if(!n.__c.__H)return!0;var i=n.__c.__H.__.filter((function(e){return!!e.__c}));if(i.every((function(e){return!e.__N})))return!o||o.call(this,e,t,r);var a=!1;return i.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(a=!0)}})),!(!a&&n.__c.props===e)&&(!o||o.call(this,e,t,r))};V.u=!0;var o=V.shouldComponentUpdate,a=V.componentWillUpdate;V.componentWillUpdate=function(e,t,r){if(this.__e){var n=o;o=void 0,i(e,t,r),o=n}a&&a.call(this,e,t,r)},V.shouldComponentUpdate=i}return n.__N||n.__}function ae(e,t){var r=ne(H++,3);!Y.__s&&ve(r.__H,t)&&(r.__=e,r.i=t,V.__H.__h.push(r))}function se(e,t){var r=ne(H++,4);!Y.__s&&ve(r.__H,t)&&(r.__=e,r.i=t,V.__h.push(r))}function ce(e,t){var r=ne(H++,7);return ve(r.__H,t)?(r.__V=e(),r.i=t,r.__h=e,r.__V):r.__}function ue(){for(var e;e=G.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(fe),e.__H.__h.forEach(de),e.__H.__h=[]}catch(t){e.__H.__h=[],Y.__e(t,e.__v)}}Y.__b=function(e){V=null,J&&J(e)},Y.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),re&&re(e,t)},Y.__r=function(e){X&&X(e),H=0;var t=(V=e.__c).__H;t&&(W===V?(t.__h=[],V.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.__V=Z,e.__N=e.i=void 0}))):(t.__h.forEach(fe),t.__h.forEach(de),t.__h=[],H=0)),W=V},Y.diffed=function(e){Q&&Q(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==G.push(t)&&K===Y.requestAnimationFrame||((K=Y.requestAnimationFrame)||pe)(ue)),t.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.__V!==Z&&(e.__=e.__V),e.i=void 0,e.__V=Z}))),W=V=null},Y.__c=function(e,t){t.some((function(e){try{e.__h.forEach(fe),e.__h=e.__h.filter((function(e){return!e.__||de(e)}))}catch(r){t.some((function(e){e.__h&&(e.__h=[])})),t=[],Y.__e(r,e.__v)}})),ee&&ee(e,t)},Y.unmount=function(e){te&&te(e);var t,r=e.__c;r&&r.__H&&(r.__H.__.forEach((function(e){try{fe(e)}catch(e){t=e}})),r.__H=void 0,t&&Y.__e(t,r.__v))};var le="function"==typeof requestAnimationFrame;function pe(e){var t,r=function(){clearTimeout(n),le&&cancelAnimationFrame(t),setTimeout(e)},n=setTimeout(r,100);le&&(t=requestAnimationFrame(r))}function fe(e){var t=V,r=e.__c;"function"==typeof r&&(e.__c=void 0,r()),V=t}function de(e){var t=V;e.__c=e.__(),V=t}function ve(e,t){return!e||e.length!==t.length||t.some((function(t,r){return t!==e[r]}))}function me(e,t){return"function"==typeof t?t(e):t}function ye(e,t){for(var r in t)e[r]=t[r];return e}function he(e,t){for(var r in e)if("__source"!==r&&!(r in t))return!0;for(var n in t)if("__source"!==n&&e[n]!==t[n])return!0;return!1}function be(e,t){this.props=e,this.context=t}(be.prototype=new g).isPureReactComponent=!0,be.prototype.shouldComponentUpdate=function(e,t){return he(this.props,e)||he(this.state,t)};var ge=t.__b;t.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),ge&&ge(e)};var _e="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911,we=function(e,t){return null==e?null:P(P(e).map(t))},ke={map:we,forEach:we,count:function(e){return e?P(e).length:0},only:function(e){var t=P(e);if(1!==t.length)throw"Children.only";return t[0]},toArray:P},Se=t.__e;t.__e=function(e,t,r,n){if(e.then)for(var i,o=t;o=o.__;)if((i=o.__c)&&i.__c)return null==t.__e&&(t.__e=r.__e,t.__k=r.__k),i.__c(e,t);Se(e,t,r,n)};var je=t.unmount;function xe(e,t,r){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),e.__c.__H=null),null!=(e=ye({},e)).__c&&(e.__c.__P===r&&(e.__c.__P=t),e.__c=null),e.__k=e.__k&&e.__k.map((function(e){return xe(e,t,r)}))),e}function Oe(e,t,r){return e&&r&&(e.__v=null,e.__k=e.__k&&e.__k.map((function(e){return Oe(e,t,r)})),e.__c&&e.__c.__P===t&&(e.__e&&r.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=r)),e}function Ee(){this.__u=0,this.t=null,this.__b=null}function Pe(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function Ae(){this.u=null,this.o=null}t.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&32&e.__u&&(e.type=null),je&&je(e)},(Ee.prototype=new g).__c=function(e,t){var r=t.__c,n=this;null==n.t&&(n.t=[]),n.t.push(r);var i=Pe(n.__v),o=!1,a=function(){o||(o=!0,r.__R=null,i?i(s):s())};r.__R=a;var s=function(){if(! --n.__u){if(n.state.__a){var e=n.state.__a;n.__v.__k[0]=Oe(e,e.__c.__P,e.__c.__O)}var t;for(n.setState({__a:n.__b=null});t=n.t.pop();)t.forceUpdate()}};n.__u++||32&t.__u||n.setState({__a:n.__b=n.__v.__k[0]}),e.then(a,a)},Ee.prototype.componentWillUnmount=function(){this.t=[]},Ee.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var r=document.createElement("div"),n=this.__v.__k[0].__c;this.__v.__k[0]=xe(this.__b,r,n.__O=n.__P)}this.__b=null}var i=t.__a&&y(b,null,e.fallback);return i&&(i.__u&=-33),[y(b,null,t.__a?null:e.children),i]};var ze=function(e,t,r){if(++r[1]===r[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(r=e.u;r;){for(;r.length>3;)r.pop()();if(r[1]<r[0])break;e.u=r=r[2]}};function Ce(e){return this.getChildContext=function(){return e.context},e.children}function Te(e){var t=this,r=e.i;t.componentWillUnmount=function(){L(null,t.l),t.l=null,t.i=null},t.i&&t.i!==r&&t.componentWillUnmount(),t.l||(t.i=r,t.l={nodeType:1,parentNode:r,childNodes:[],appendChild:function(e){this.childNodes.push(e),t.i.appendChild(e)},insertBefore:function(e,r){this.childNodes.push(e),t.i.appendChild(e)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.i.removeChild(e)}}),L(y(Ce,{context:t.context},e.__v),t.l)}(Ae.prototype=new g).__a=function(e){var t=this,r=Pe(t.__v),n=t.o.get(e);return n[0]++,function(i){var o=function(){t.props.revealOrder?(n.push(i),ze(t,e,n)):i()};r?r(o):o()}},Ae.prototype.render=function(e){this.u=null,this.o=new Map;var t=P(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var r=t.length;r--;)this.o.set(t[r],this.u=[1,0,this.u]);return e.children},Ae.prototype.componentDidUpdate=Ae.prototype.componentDidMount=function(){var e=this;this.o.forEach((function(t,r){ze(e,r,t)}))};var Ne="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,Ie=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,De=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,Re=/[A-Z0-9]/g,Me="undefined"!=typeof document,qe=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(e)};function Ue(e,t,r){return null==t.__k&&(t.textContent=""),L(e,t),"function"==typeof r&&r(),e?e.__c:null}g.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(g.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var Le=t.event;function Fe(){}function Be(){return this.cancelBubble}function He(){return this.defaultPrevented}t.event=function(e){return Le&&(e=Le(e)),e.persist=Fe,e.isPropagationStopped=Be,e.isDefaultPrevented=He,e.nativeEvent=e};var Ve,We={enumerable:!1,configurable:!0,get:function(){return this.class}},Ke=t.vnode;t.vnode=function(e){"string"==typeof e.type&&function(e){var t=e.props,r=e.type,n={};for(var i in t){var o=t[i];if(!("value"===i&&"defaultValue"in t&&null==o||Me&&"children"===i&&"noscript"===r||"class"===i||"className"===i)){var a=i.toLowerCase();"defaultValue"===i&&"value"in t&&null==t.value?i="value":"download"===i&&!0===o?o="":"translate"===a&&"no"===o?o=!1:"ondoubleclick"===a?i="ondblclick":"onchange"!==a||"input"!==r&&"textarea"!==r||qe(t.type)?"onfocus"===a?i="onfocusin":"onblur"===a?i="onfocusout":De.test(i)?i=a:-1===r.indexOf("-")&&Ie.test(i)?i=i.replace(Re,"-$&").toLowerCase():null===o&&(o=void 0):a=i="oninput","oninput"===a&&n[i=a]&&(i="oninputCapture"),n[i]=o}}"select"==r&&n.multiple&&Array.isArray(n.value)&&(n.value=P(t.children).forEach((function(e){e.props.selected=-1!=n.value.indexOf(e.props.value)}))),"select"==r&&null!=n.defaultValue&&(n.value=P(t.children).forEach((function(e){e.props.selected=n.multiple?-1!=n.defaultValue.indexOf(e.props.value):n.defaultValue==e.props.value}))),t.class&&!t.className?(n.class=t.class,Object.defineProperty(n,"className",We)):(t.className&&!t.class||t.class&&t.className)&&(n.class=n.className=t.className),e.props=n}(e),e.$$typeof=Ne,Ke&&Ke(e)};var $e=t.__r;t.__r=function(e){$e&&$e(e),Ve=e.__c};var Ge=t.diffed;t.diffed=function(e){Ge&&Ge(e);var t=e.props,r=e.__e;null!=r&&"textarea"===e.type&&"value"in t&&t.value!==r.value&&(r.value=null==t.value?"":t.value),Ve=null};var Ze={ReactCurrentDispatcher:{current:{readContext:function(e){return Ve.__n[e.__c].props.value}}}};function Ye(e){return!!e&&e.$$typeof===Ne}function Je(e){e()}function Xe(e){var t,r,n=e.v,i=e.__;try{var o=n();return!((t=i)===(r=o)&&(0!==t||1/t==1/r)||t!=t&&r!=r)}catch(e){return!0}}var Qe={useState:ie,useId:function(){var e=ne(H++,11);if(!e.__){for(var t=V.__v;null!==t&&!t.__m&&null!==t.__;)t=t.__;var r=t.__m||(t.__m=[0,0]);e.__="P"+r[0]+"-"+r[1]++}return e.__},useReducer:oe,useEffect:ae,useLayoutEffect:se,useInsertionEffect:se,useTransition:function(){return[!1,Je]},useDeferredValue:function(e){return e},useSyncExternalStore:function(e,t){var r=t(),n=ie({h:{__:r,v:t}}),i=n[0].h,o=n[1];return se((function(){i.__=r,i.v=t,Xe(i)&&o({h:i})}),[e,r,t]),ae((function(){return Xe(i)&&o({h:i}),e((function(){Xe(i)&&o({h:i})}))}),[e]),r},startTransition:Je,useRef:function(e){return $=5,ce((function(){return{current:e}}),[])},useImperativeHandle:function(e,t,r){$=6,se((function(){return"function"==typeof e?(e(t()),function(){return e(null)}):e?(e.current=t(),function(){return e.current=null}):void 0}),null==r?r:r.concat(e))},useMemo:ce,useCallback:function(e,t){return $=8,ce((function(){return e}),t)},useContext:function(e){var t=V.context[e.__c],r=ne(H++,9);return r.c=e,t?(null==r.__&&(r.__=!0,t.sub(V)),t.props.value):e.__},useDebugValue:function(e,t){Y.useDebugValue&&Y.useDebugValue(t?t(e):e)},version:"17.0.2",Children:ke,render:Ue,hydrate:function(e,t,r){return F(e,t),"function"==typeof r&&r(),e?e.__c:null},unmountComponentAtNode:function(e){return!!e.__k&&(L(null,e),!0)},createPortal:function(e,t){var r=y(Te,{__v:e,i:t});return r.containerInfo=t,r},createElement:y,createContext:function(e,t){var r={__c:t="__cC"+u++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var r,n;return this.getChildContext||(r=[],(n={})[t]=this,this.getChildContext=function(){return n},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&r.some((function(e){e.__e=!0,S(e)}))},this.sub=function(e){r.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){r.splice(r.indexOf(e),1),t&&t.call(e)}}),e.children}};return r.Provider.__=r.Consumer.contextType=r},createFactory:function(e){return y.bind(null,e)},cloneElement:function(e){return Ye(e)?B.apply(null,arguments):e},createRef:function(){return{current:null}},Fragment:b,isValidElement:Ye,isElement:Ye,isFragment:function(e){return Ye(e)&&e.type===b},findDOMNode:function(e){return e&&(e.base||1===e.nodeType&&e)||null},Component:g,PureComponent:be,memo:function(e,t){function r(e){var r=this.props.ref,n=r==e.ref;return!n&&r&&(r.call?r(null):r.current=null),t?!t(this.props,e)||!n:he(this.props,e)}function n(t){return this.shouldComponentUpdate=r,y(e,t)}return n.displayName="Memo("+(e.displayName||e.name)+")",n.prototype.isReactComponent=!0,n.__f=!0,n},forwardRef:function(e){function t(t){var r=ye({},t);return delete r.ref,e(r,t.ref||null)}return t.$$typeof=_e,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t},flushSync:function(e,t){return e(t)},unstable_batchedUpdates:function(e,t){return e(t)},StrictMode:b,Suspense:Ee,SuspenseList:Ae,lazy:function(e){var t,r,n;function i(i){if(t||(t=e()).then((function(e){r=e.default||e}),(function(e){n=e})),n)throw n;if(!r)throw t;return y(r,i)}return i.displayName="Lazy",i.__f=!0,i},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Ze},et=(r(6437),r(2697),r(1359),r(6216),r(6448),r(7089),r(2688)),tt=function(e){var t=e.t;return Qe.createElement("svg",{role:"img","aria-label":t(["close"]),width:"12",height:"12",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},Qe.createElement("title",null,t(["close"])),Qe.createElement("line",{x1:"1",y1:"11",x2:"11",y2:"1",strokeWidth:"1"}),Qe.createElement("line",{x1:"1",y1:"1",x2:"11",y2:"11",strokeWidth:"1"}))};function rt(e){return e.split("-").map((function(e){return e.slice(0,1).toUpperCase()+e.slice(1)})).join(" ")}function nt(e){return function(e){if(Array.isArray(e))return it(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return it(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?it(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function it(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ot(e){return ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ot(e)}tt.propTypes={t:r.n(et)().func},r(7575),r(646),r(987),r(3819);var at=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i,o=ot(r[0]);i=0===r.length?{}:"string"===o||"number"===o?Array.prototype.slice.call(r):r[0];for(var a=[],s=e.toString();s.length>0;){var c=s.match(/\{(?!\{)([\w\d]+)\}(?!\})/);if(null!==c){var u=s.substr(0,c.index);s=s.substr(c.index+c[0].length);var l=parseInt(c[1]);a.push(u),l!=l?a.push(i[c[1]]):a.push(i[l])}else a.push(s),s=""}return a};function st(e){if(void 0!==e&&void 0!==e.lang&&"zz"!==e.lang)return e.lang;var t=(("string"==typeof window.language?window.language:null)||document.documentElement.lang||(void 0!==e&&void 0!==e.languages&&void 0!==e.languages[0]?e.languages[0]:"en")).toLowerCase(),r=new RegExp("^([\\w]+)-([\\w]+)$").exec(t);return null===r?t:r[1]}function ct(e,t,r){var n=t;Array.isArray(n)||(n=[n]);for(var i=e,o=0;o<n.length;o++){if(void 0===i)return r;if(void 0!==n[o]&&n[o].endsWith("?")){var a,s=n[o].slice(0,n[o].length-1);void 0!==(a=i instanceof Map?i.get(s):i[s])&&"string"==typeof a&&(i=a)}else i=i instanceof Map?i.get(n[o]):i[n[o]]}return void 0===i||"string"!=typeof i?r:""!==i?i:void 0}function ut(e,t,r,n){var i=n,o=!1;"!"===i[0]&&(i=i.slice(1),o=!0),Array.isArray(i)||(i=[i]);var a=ct(e,[t].concat(nt(i)));if(void 0===a&&void 0!==r&&(a=ct(e,[r].concat(nt(i)))),void 0===a){if(o)return;return["[missing translation: ".concat(t,"/").concat(i.join("/"),"]")]}for(var s=arguments.length,c=new Array(s>4?s-4:0),u=4;u<s;u++)c[u-4]=arguments[u];return c.length>0?at.apply(void 0,[a].concat(c)):a}const lt=function(e){var t=e.text,r=e.config;if(t instanceof Array||(t=[t]),!0===r.htmlTexts){var n=!1;"<"===t[0][0]&&(n=!0);var i=t.map((function(e,t){return"string"==typeof e?Qe.createElement("span",{key:t,dangerouslySetInnerHTML:{__html:e}}):e}));return n?Qe.createElement(Qe.Fragment,null,i):Qe.createElement("span",null,i)}return Qe.createElement("span",null,t)};function pt(e){return pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pt(e)}function ft(){return ft=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ft.apply(this,arguments)}function dt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,vt(n.key),n)}}function vt(e){var t=function(e,t){if("object"!=pt(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=pt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==pt(t)?t:String(t)}function mt(e,t,r){return t=ht(t),function(e,t){if(t&&("object"===pt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,yt()?Reflect.construct(t,r||[],ht(e).constructor):t.apply(e,r))}function yt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(yt=function(){return!!e})()}function ht(e){return ht=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ht(e)}function bt(e,t){return bt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},bt(e,t)}var gt=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),mt(this,t,arguments)}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&bt(e,t)}(t,e),r=t,n=[{key:"render",value:function(){var e,t=this.props,r=t.checked,n=t.onlyRequiredEnabled,i=t.onToggle,o=t.name,a=t.lang,s=t.config,c=t.translations,u=t.title,l=t.description,p=t.visible,f=t.t,d=this.props.required||!1,v=this.props.optOut||!1,m=this.props.purposes||[],y="service-item-".concat(o),h="".concat(y,"-title"),b=m.map((function(e){return f(["!","purposes",e,"title?"])||rt(e)})).join(", "),g=v?Qe.createElement("span",{className:"cm-opt-out",title:f(["service","optOut","description"])},f(["service","optOut","title"])):"",_=d?Qe.createElement("span",{className:"cm-required",title:f(["service","required","description"])},f(["service","required","title"])):"";m.length>0&&(e=Qe.createElement("p",{className:"purposes"},f(["service",m.length>1?"purposes":"purpose"]),": ",b));var w=l||ut(c,a,"zz",["!","description"])||f(["!",o,"description?"]);return Qe.createElement("div",null,Qe.createElement("input",{id:y,className:"cm-list-input"+(d?" required":"")+(n?" half-checked only-required":""),"aria-labelledby":"".concat(h),"aria-describedby":"".concat(y,"-description"),disabled:d,checked:r||d,tabIndex:p?"0":"-1",type:"checkbox",onChange:function(e){i(e.target.checked)}}),Qe.createElement("label",ft({htmlFor:y,className:"cm-list-label"},d?{tabIndex:"0"}:{}),Qe.createElement("span",{className:"cm-list-title",id:"".concat(h)},u||ut(c,a,"zz",["!","title"])||f(["!",o,"title?"])||rt(o)),_,g,Qe.createElement("span",{className:"cm-switch"},Qe.createElement("div",{className:"slider round active"}))),Qe.createElement("div",{id:"".concat(y,"-description")},w&&Qe.createElement("p",{className:"cm-list-description"},Qe.createElement(lt,{config:s,text:w})),e))}}],n&&dt(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function _t(e){return _t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_t(e)}function wt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,kt(n.key),n)}}function kt(e){var t=function(e,t){if("object"!=_t(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=_t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==_t(t)?t:String(t)}function St(e,t,r){return t=xt(t),function(e,t){if(t&&("object"===_t(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ot(e)}(e,jt()?Reflect.construct(t,r||[],xt(e).constructor):t.apply(e,r))}function jt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(jt=function(){return!!e})()}function xt(e){return xt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},xt(e)}function Ot(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Et(e,t){return Et=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Et(e,t)}function Pt(){return Pt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pt.apply(this,arguments)}var At=function(e){var t=e.services,r=e.config,n=e.consents,i=e.lang,o=e.toggle,a=e.visible,s=e.t;return t.map((function(e){var t=n[e.name];return Qe.createElement("li",{key:e.name,className:"cm-service"},Qe.createElement(gt,Pt({checked:t||e.required,onToggle:function(t){o([e],t)},config:r,lang:i,visible:a,t:s},e)))}))},zt=function(e){function t(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),r=St(this,t,[e]),e.manager.watch(Ot(r)),r.state={consents:e.manager.consents},r}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Et(e,t)}(t,e),r=t,(n=[{key:"componentWillUnmount",value:function(){this.props.manager.unwatch(this)}},{key:"update",value:function(e,t,r){e===this.props.manager&&"consents"===t&&this.setState({consents:r})}},{key:"render",value:function(){var e=this.props,t=e.config,r=e.t,n=e.manager,i=e.lang,o=this.state.consents,a=t.services,s=function(e,t){e.map((function(e){e.required||n.updateConsent(e.name,t)}))},c=Qe.createElement(At,{config:t,lang:i,services:a,t:r,consents:o,toggle:s}),u=a.filter((function(e){return!e.required})),l=u.filter((function(e){return o[e.name]})).length,p=a.filter((function(e){return e.required})).length,f=l===u.length;return a.filter((function(e){return e.required})).length,Qe.createElement("ul",{className:"cm-services"},c,!t.hideToggleAll&&u.length>1&&Qe.createElement("li",{className:"cm-service cm-toggle-all"},Qe.createElement(gt,{name:"disableAll",title:r(["service","disableAll","title"]),description:r(["service","disableAll","description"]),checked:f,config:t,onlyRequiredEnabled:!f&&p>0,onToggle:function(e){s(a,e)},lang:i,t:r})))}}])&&wt(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function Ct(e){return Ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ct(e)}function Tt(){return Tt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Tt.apply(this,arguments)}function Nt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,It(n.key),n)}}function It(e){var t=function(e,t){if("object"!=Ct(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=Ct(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Ct(t)?t:String(t)}function Dt(e,t,r){return t=Mt(t),function(e,t){if(t&&("object"===Ct(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Rt()?Reflect.construct(t,r||[],Mt(e).constructor):t.apply(e,r))}function Rt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Rt=function(){return!!e})()}function Mt(e){return Mt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Mt(e)}function qt(e,t){return qt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},qt(e,t)}var Ut=function(e){function t(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(r=Dt(this,t,[e])).state={servicesVisible:!1},r}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qt(e,t)}(t,e),r=t,n=[{key:"render",value:function(){var e,t=this,r=this.props,n=r.allEnabled,i=r.onlyRequiredEnabled,o=r.allDisabled,a=r.services,s=r.config,c=r.onToggle,u=r.name,l=r.lang,p=r.manager,f=r.consents,d=r.title,v=r.description,m=r.t,y=this.state.servicesVisible,h=this.props.required||!1,b=this.props.purposes||[],g="purpose-item-".concat(u),_="".concat(g,"-title"),w=b.map((function(e){return m(["!","purposes",e,"title?"])||rt(e)})).join(", "),k=h?Qe.createElement("span",{className:"cm-required",title:m(["!","service","required","description"])||""},m(["service","required","title"])):"";b.length>0&&(e=Qe.createElement("p",{className:"purposes"},m(["purpose",b.length>1?"purposes":"purpose"]),": ",w));var S=function(e){e.preventDefault();var r="false"!==e.currentTarget.getAttribute("aria-expanded");e.currentTarget.setAttribute("aria-expanded",!r),t.setState({servicesVisible:!y})},j=Qe.createElement(At,{config:s,lang:l,services:a,toggle:function(e,t){e.map((function(e){e.required||p.updateConsent(e.name,t)}))},consents:f,visible:y,t:m}),x=v||m(["!","purposes",u,"description"]);return Qe.createElement(Qe.Fragment,null,Qe.createElement("input",{id:g,className:"cm-list-input"+(h?" required":"")+(n?"":i?" only-required":" half-checked"),"aria-labelledby":"".concat(_),"aria-describedby":"".concat(g,"-description"),disabled:h,checked:n||!o&&!i,type:"checkbox",onChange:function(e){c(e.target.checked)}}),Qe.createElement("label",Tt({htmlFor:g,className:"cm-list-label"},h?{tabIndex:"0"}:{}),Qe.createElement("span",{className:"cm-list-title",id:"".concat(_)},d||m(["!","purposes",u,"title?"])||rt(u)),k,Qe.createElement("span",{className:"cm-switch"},Qe.createElement("div",{className:"slider round active"}))),Qe.createElement("div",{id:"".concat(g,"-description")},x&&Qe.createElement("p",{className:"cm-list-description"},Qe.createElement(lt,{config:s,text:x})),e),a.length>0&&Qe.createElement("div",{className:"cm-services"},Qe.createElement("div",{className:"cm-caret"},Qe.createElement("a",{href:"#","aria-haspopup":"true","aria-expanded":"false",tabIndex:"0",onClick:S,onKeyDown:function(e){32===e.keyCode&&S(e)}},y&&Qe.createElement("span",null,"↑")||Qe.createElement("span",null,"↓")," ",a.length," ",m(["purposeItem",a.length>1?"services":"service"]))),Qe.createElement("ul",{className:"cm-content"+(y?" expanded":"")},j)))}}],n&&Nt(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function Lt(e){return Lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Lt(e)}function Ft(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return Bt(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Bt(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw o}}}}function Bt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ht(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Vt(n.key),n)}}function Vt(e){var t=function(e,t){if("object"!=Lt(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=Lt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Lt(t)?t:String(t)}function Wt(e,t,r){return t=$t(t),function(e,t){if(t&&("object"===Lt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Gt(e)}(e,Kt()?Reflect.construct(t,r||[],$t(e).constructor):t.apply(e,r))}function Kt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Kt=function(){return!!e})()}function $t(e){return $t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},$t(e)}function Gt(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Zt(e,t){return Zt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Zt(e,t)}var Yt=function(e){function t(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),r=Wt(this,t,[e]),e.manager.watch(Gt(r)),r.state={consents:e.manager.consents},r}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Zt(e,t)}(t,e),r=t,n=[{key:"componentWillUnmount",value:function(){this.props.manager.unwatch(this)}},{key:"update",value:function(e,t,r){e===this.props.manager&&"consents"===t&&this.setState({consents:r})}},{key:"render",value:function(){var e,t=this.props,r=t.config,n=t.t,i=t.manager,o=t.lang,a=this.state.consents,s=r.services,c={},u=Ft(s);try{for(u.s();!(e=u.n()).done;){var l,p=e.value,f=Ft(p.purposes);try{for(f.s();!(l=f.n()).done;){var d=l.value;void 0===c[d]&&(c[d]=[]),c[d].push(p)}}catch(e){f.e(e)}finally{f.f()}}}catch(e){u.e(e)}finally{u.f()}var v=function(e,t){e.map((function(e){var r,n=Ft(c[e]);try{for(n.s();!(r=n.n()).done;){var o=r.value;o.required||i.updateConsent(o.name,t)}}catch(e){n.e(e)}finally{n.f()}}))},m=function(e){var t,r={allEnabled:!0,onlyRequiredEnabled:!0,allDisabled:!0,allRequired:!0},n=Ft(e);try{for(n.s();!(t=n.n()).done;){var i=t.value;i.required||(r.allRequired=!1),a[i.name]?(i.required||(r.onlyRequiredEnabled=!1),r.allDisabled=!1):i.required||(r.allEnabled=!1)}}catch(e){n.e(e)}finally{n.f()}return r.allDisabled&&(r.onlyRequiredEnabled=!1),r},y=r.purposeOrder||[],h=Object.keys(c).sort((function(e,t){return y.indexOf(e)-y.indexOf(t)})).map((function(e){var t=m(c[e]);return Qe.createElement("li",{key:e,className:"cm-purpose"},Qe.createElement(Ut,{allEnabled:t.allEnabled,allDisabled:t.allDisabled,onlyRequiredEnabled:t.onlyRequiredEnabled,required:t.allRequired,consents:a,name:e,config:r,lang:o,manager:i,onToggle:function(t){v([e],t)},services:c[e],t:n}))})),b=Object.keys(c).filter((function(e){var t,r=Ft(c[e]);try{for(r.s();!(t=r.n()).done;)if(!t.value.required)return!0}catch(e){r.e(e)}finally{r.f()}return!1})),g=m(s);return Qe.createElement("ul",{className:"cm-purposes"},h,b.length>1&&Qe.createElement("li",{className:"cm-purpose cm-toggle-all"},Qe.createElement(Ut,{name:"disableAll",title:n(["service","disableAll","title"]),description:n(["service","disableAll","description"]),allDisabled:g.allDisabled,allEnabled:g.allEnabled,onlyRequiredEnabled:g.onlyRequiredEnabled,onToggle:function(e){v(Object.keys(c),e)},manager:i,consents:a,config:r,lang:o,services:[],t:n})))}}],n&&Ht(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function Jt(e){return Jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Jt(e)}function Xt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Qt(n.key),n)}}function Qt(e){var t=function(e,t){if("object"!=Jt(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=Jt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Jt(t)?t:String(t)}function er(e,t,r){return t=rr(t),function(e,t){if(t&&("object"===Jt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,tr()?Reflect.construct(t,r||[],rr(e).constructor):t.apply(e,r))}function tr(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(tr=function(){return!!e})()}function rr(e){return rr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},rr(e)}function nr(e,t){return nr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},nr(e,t)}var ir=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),er(this,t,arguments)}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nr(e,t)}(t,e),r=t,(n=[{key:"componentDidMount",value:function(){this.consentModalRef&&this.consentModalRef.focus()}},{key:"render",value:function(){var e,t,r,n=this,i=this.props,o=i.hide,a=i.confirming,s=i.saveAndHide,c=i.acceptAndHide,u=i.declineAndHide,l=i.config,p=i.manager,f=i.lang,d=i.t,v=l.embedded,m=void 0===l.groupByPurpose||l.groupByPurpose;l.mustConsent||(e=Qe.createElement("button",{title:d(["close"]),"aria-label":d(["close"]),className:"hide",type:"button",onClick:o,tabIndex:"0",ref:function(e){n.consentModalRef=e}},Qe.createElement(tt,{t:d}))),l.hideDeclineAll||p.confirmed||(t=Qe.createElement("button",{disabled:a,className:"cm-btn cm-btn-decline cm-btn-danger cn-decline",type:"button",onClick:u},d(["decline"])));var y,h,b,g=Qe.createElement("button",{disabled:a,className:"cm-btn cm-btn-success cm-btn-info cm-btn-accept",type:"button",onClick:s},d([p.confirmed?"save":"acceptSelected"]));l.acceptAll&&!p.confirmed&&(r=Qe.createElement("button",{disabled:a,className:"cm-btn cm-btn-success cm-btn-accept-all",type:"button",onClick:c},d(["acceptAll"]))),void 0!==l.privacyPolicy?"string"==typeof l.privacyPolicy?y=l.privacyPolicy:"object"===Jt(l.privacyPolicy)&&(y=l.privacyPolicy[f]||l.privacyPolicy.default):void 0!==(y=d(["!","privacyPolicyUrl"],{lang:f}))&&(y=y.join("")),void 0!==y&&(h=Qe.createElement("a",{key:"ppLink",href:y,target:"_blank",rel:"noopener"},d(["privacyPolicy","name"]))),b=m?Qe.createElement(Yt,{t:d,config:l,manager:p,lang:f}):Qe.createElement(zt,{t:d,config:l,manager:p,lang:f});var _=Qe.createElement("div",{className:"cm-modal cm-klaro"},Qe.createElement("div",{className:"cm-header"},e,Qe.createElement("h1",{className:"title"},Qe.createElement(lt,{config:l,text:d(["consentModal","title"])})),Qe.createElement("p",null,Qe.createElement(lt,{config:l,text:[d(["consentModal","description"])].concat(h&&[" "].concat(d(["privacyPolicy","text"],{privacyPolicy:h}))||[])}))),Qe.createElement("div",{className:"cm-body"},b),Qe.createElement("div",{className:"cm-footer"},Qe.createElement("div",{className:"cm-footer-buttons"},t,g,r),!l.disablePoweredBy&&Qe.createElement("p",{className:"cm-powered-by"},Qe.createElement("a",{target:"_blank",href:l.poweredBy||"https://kiprotect.com/klaro",rel:"noopener"},d(["poweredBy"])))));return v?Qe.createElement("div",{id:"cookieScreen",className:"cookie-modal cm-embedded"},_):Qe.createElement("div",{id:"cookieScreen",className:"cookie-modal"},Qe.createElement("div",{className:"cm-bg",onClick:o}),_)}}])&&Xt(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function or(e){return or="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},or(e)}function ar(e,t,r){void 0===r&&(r=!0);for(var n=Object.keys(t),i=0;i<n.length;i++){var o=n[i],a=t[o],s=e[o];"string"==typeof a?(r||void 0===s)&&(e[o]=a):"object"===or(a)&&("object"===or(s)?ar(s,a,r):(r||void 0===s)&&(e[o]=a))}return e}function sr(e){return sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sr(e)}function cr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ur(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yr(n.key),n)}}function lr(e,t,r){return t=fr(t),function(e,t){if(t&&("object"===sr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return dr(e)}(e,pr()?Reflect.construct(t,r||[],fr(e).constructor):t.apply(e,r))}function pr(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(pr=function(){return!!e})()}function fr(e){return fr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},fr(e)}function dr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function vr(e,t){return vr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},vr(e,t)}function mr(e,t,r){return(t=yr(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yr(e){var t=function(e,t){if("object"!=sr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=sr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==sr(t)?t:String(t)}r(2745);var hr=function(e){function t(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),mr(dr(r=lr(this,t,[e])),"executeButtonClicked",(function(e,t,n){var i=r.state.modal,o=0;e&&(o=r.props.manager.changeAll(t));var a=r.props.manager.confirmed;if(r.props.manager.saveAndApplyConsents(n),e&&!a&&(i||r.props.config.mustConsent)){var s=function(){r.setState({confirming:!1}),r.props.hide()};r.setState({confirming:!0}),0===o?s():setTimeout(s,800)}else r.props.hide()})),mr(dr(r),"saveAndHide",(function(){r.executeButtonClicked(!1,!1,"save")})),mr(dr(r),"acceptAndHide",(function(){r.executeButtonClicked(!0,!0,"accept")})),mr(dr(r),"declineAndHide",(function(){r.executeButtonClicked(!0,!1,"decline")})),r.state={modal:e.modal,confirming:!1},r}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&vr(e,t)}(t,e),r=t,n=[{key:"componentDidUpdate",value:function(e){e.modal!==this.props.modal&&this.setState({modal:this.props.modal}),this.noticeRef&&this.noticeRef.focus()}},{key:"render",value:function(){var e,t,r,n=this,i=this.props,o=i.lang,a=i.config,s=i.show,c=i.manager,u=i.testing,l=i.t,p=this.state,f=p.confirming,d=p.modal,v=a.embedded,m=a.noticeAsModal,y=a.hideLearnMore,h=a.purposeOrder||[],b=function(e){for(var t=new Set([]),r=0;r<e.services.length;r++)for(var n=e.services[r].purposes||[],i=0;i<n.length;i++)t.add(n[i]);return Array.from(t)}(a).filter((function(e){return"functional"!==e})).sort((function(e,t){return h.indexOf(e)-h.indexOf(t)})),g=b.map((function(e){return l(["!","purposes",e,"title?"])||rt(e)}));t=1===g.length?g[0]:[].concat((r=g.slice(0,-2),function(e){if(Array.isArray(e))return cr(e)}(r)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(r)||function(e,t){if(e){if("string"==typeof e)return cr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?cr(e,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),[g.slice(-2).join(" & ")]).join(", "),void 0!==a.privacyPolicy?"string"==typeof a.privacyPolicy?e=a.privacyPolicy:"object"===sr(a.privacyPolicy)&&(e=a.privacyPolicy[o]||a.privacyPolicy.default):void 0!==(e=l(["!","privacyPolicyUrl"],{lang:o}))&&(e=e.join(""));var _,w=function(e){e.preventDefault(),n.setState({modal:!0})};if(c.changed&&(_=Qe.createElement("p",{className:"cn-changes"},l(["consentNotice","changeDescription"]))),!s&&!u&&!f)return Qe.createElement("div",null);var k,S=(!a.mustConsent||m)&&!c.confirmed&&!a.noNotice,j=a.hideDeclineAll?"":Qe.createElement("button",{className:"cm-btn cm-btn-danger cn-decline",type:"button",onClick:this.declineAndHide},l(["decline"])),x=a.acceptAll?Qe.createElement("button",{className:"cm-btn cm-btn-success",type:"button",onClick:this.acceptAndHide},l(["ok"])):Qe.createElement("button",{className:"cm-btn cm-btn-success",type:"button",onClick:this.saveAndHide},l(["ok"])),O=function(){return m?Qe.createElement("button",{key:"learnMoreLink",className:"cm-btn cm-btn-lern-more cm-btn-info",type:"button",onClick:w},l(["consentNotice","learnMore"])):Qe.createElement("a",{key:"learnMoreLink",className:"cm-link cn-learn-more",href:"#",onClick:w},l(["consentNotice","learnMore"]))};if(void 0!==e&&(k=Qe.createElement("a",{key:"ppLink",href:e},l(["privacyPolicy","name"]))),d||c.confirmed&&!u||!c.confirmed&&a.mustConsent)return Qe.createElement(ir,{t:l,lang:o,config:a,hide:function(){a.mustConsent&&!a.acceptAll||(c.confirmed&&!u?n.props.hide():n.setState({modal:!1}),setTimeout((function(){n.noticeRef&&n.noticeRef.focus()}),1))},confirming:f,declineAndHide:this.declineAndHide,saveAndHide:this.saveAndHide,acceptAndHide:this.acceptAndHide,manager:c});var E=Qe.createElement("div",{role:"dialog","aria-describedby":"id-cookie-notice","aria-labelledby":"id-cookie-title",id:"klaro-cookie-notice",tabIndex:"0",autofocus:a.autoFocus,ref:function(e){n.noticeRef=e},className:"cookie-notice ".concat(S||u?"":"cookie-notice-hidden"," ").concat(m?"cookie-modal-notice":""," ").concat(v?"cn-embedded":"")},Qe.createElement("div",{className:"cn-body"},l(["!","consentNotice","title"])&&a.showNoticeTitle&&Qe.createElement("h2",{id:"id-cookie-title"},l(["consentNotice","title"])),Qe.createElement("p",{id:"id-cookie-notice"},Qe.createElement(lt,{config:a,text:l(["consentNotice","description"],{purposes:Qe.createElement("strong",{key:"strong"},t),privacyPolicy:k,learnMoreLink:O()})})),u&&Qe.createElement("p",null,l(["consentNotice","testing"])),_,Qe.createElement("div",{className:"cn-ok"},!y&&O(),Qe.createElement("div",{className:"cn-buttons"},j,x))));return m?Qe.createElement("div",{id:"cookieScreen",className:"cookie-modal"},Qe.createElement("div",{className:"cm-bg"}),E):E}}],n&&ur(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function br(e){return br="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},br(e)}function gr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_r(n.key),n)}}function _r(e){var t=function(e,t){if("object"!=br(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=br(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==br(t)?t:String(t)}function wr(e,t,r){return t=Sr(t),function(e,t){if(t&&("object"===br(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return jr(e)}(e,kr()?Reflect.construct(t,r||[],Sr(e).constructor):t.apply(e,r))}function kr(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(kr=function(){return!!e})()}function Sr(e){return Sr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Sr(e)}function jr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xr(e,t){return xr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},xr(e,t)}var Or=function(e){function t(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),r=wr(this,t,[e]),e.manager.watch(jr(r)),r.state={show:e.show>0||!e.manager.confirmed},r}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xr(e,t)}(t,e),r=t,n=[{key:"componentWillUnmount",value:function(){this.props.manager.unwatch(this)}},{key:"update",value:function(e,t){e===this.props.manager&&"applyConsents"===t&&(!this.props.config.embedded&&this.props.manager.confirmed?this.setState({show:!1}):this.forceUpdate())}},{key:"notifyApi",value:function(){var e=this.props,t=e.api,r=e.modal,n=e.show,i=e.config;if(void 0!==t){if(r||n>0)return;this.props.manager.confirmed||this.props.manager.auxiliaryStore.getWithKey("shown-before")||(t.update(this,"showNotice",{config:i}),this.props.manager.auxiliaryStore.setWithKey("shown-before",!0))}}},{key:"componentDidMount",value:function(){this.notifyApi()}},{key:"componentDidUpdate",value:function(e){if(e.show!==this.props.show){this.notifyApi();var t=this.props.show>0||!this.props.manager.confirmed;t!==this.state.show&&this.setState({show:t})}}},{key:"render",value:function(){var e=this,t=this.props,r=t.config,n=t.t,i=t.lang,o=t.testing,a=t.manager,s=t.modal,c=this.state.show,u=r.additionalClass,l=r.embedded,p=r.stylePrefix;return Qe.createElement("div",{lang:i,className:(p||"klaro")+(void 0!==u?" "+u:"")},Qe.createElement(hr,{key:"app-"+this.props.show,t:n,testing:o,show:c,lang:i,modal:s,hide:function(){l||e.setState({show:!1})},config:r,manager:a}))}}],n&&gr(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function Er(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}const Pr=function(e){var t=e.manager,r=e.style,n=e.config,i=e.t,o=e.lang,a=e.service,s=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,a,s=[],c=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Er(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Er(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(ie(0),2),c=s[0],u=s[1],l=n.additionalClass,p=(n.embedded,n.stylePrefix);ae((function(){var e={update:function(){return u(c+1)}};return t.watch(e),function(){t.unwatch(e)}}));var f=ut(a.translations||{},o,"zz",["!","title"])||i(["!",a.name,"title?"])||rt(a.name);return Qe.createElement("div",{lang:o,className:(p||"klaro")+(void 0!==l?" "+l:"")+" cm-as-context-notice"},Qe.createElement("div",{className:"context-notice"+(void 0!==r?" cm-".concat(r):"")},Qe.createElement("p",null,i(["contextualConsent","description"],{title:f})),Qe.createElement("p",{className:"cm-buttons"},Qe.createElement("button",{className:"cm-btn cm-btn-success",type:"button",onClick:function(){t.updateConsent(a.name,!0),t.applyConsents(!1,!0,a.name),t.updateConsent(a.name,!1)}},i(["contextualConsent","acceptOnce"])),null!==t.store.get()?Qe.createElement("button",{className:"cm-btn cm-btn-success-var",type:"button",onClick:function(){t.updateConsent(a.name,!0),t.confirmed?(t.saveConsents("contextual-accept"),t.applyConsents(!1,!0,a.name)):t.applyConsents(!1,!0,a.name)}},i(["contextualConsent","acceptAlways"])):""),null===t.store.get()&&n.showDescriptionEmptyStore?Qe.createElement(Qe.Fragment,null,Qe.createElement("p",{className:"ccn-description-empty-store"},i(["contextualConsent","descriptionEmptyStore"],{title:f,link:Qe.createElement("a",{key:"modalLink",className:"ccn-modal-link",href:"#",onClick:function(e){e.preventDefault(),fn(n,!0)}},i(["contextualConsent","modalLinkText"]))}))):""))};var Ar=r(2690);function zr(e){return zr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zr(e)}function Cr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Tr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Cr(Object(r),!0).forEach((function(t){var n,i,o;n=e,i=t,o=r[t],(i=Ir(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Nr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ir(n.key),n)}}function Ir(e){var t=function(e,t){if("object"!=zr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=zr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==zr(t)?t:String(t)}r(76);var Dr=function(){function e(t,r,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.url=t,this.id=r,this.opts=Object.assign({},n)}var t,r;return t=e,(r=[{key:"getLocationData",value:function(e){var t=e.records||{};return{pathname:void 0===t.savePathname||t.savePathname?location.pathname:void 0,port:""!==location.port?parseInt(location.port):0,hostname:location.hostname,protocol:location.protocol.slice(0,location.protocol.length-1)}}},{key:"getUserData",value:function(){return{client_version:yn(),client_name:"klaro:web"}}},{key:"getBaseConsentData",value:function(e){return{location_data:this.getLocationData(e),user_data:this.getUserData(e)}}},{key:"update",value:function(e,t,r){if("saveConsents"===t){if("save"===r.type&&0===Object.keys(r.changes).length)return;var n=Tr(Tr({},this.getBaseConsentData(e.config)),{},{consent_data:{consents:r.consents,changes:"save"===r.type?r.changes:void 0,type:r.type,config:e.config.id}});this.submitConsentData(n)}else if("showNotice"===t){var i=Tr(Tr({},this.getBaseConsentData(r.config)),{},{consent_data:{consents:{},changes:{},type:"show",config:r.config.id}});this.submitConsentData(i)}}},{key:"apiRequest",value:function(e,t,r,n){var i=this;return new Promise((function(o,a){var s,c,u=new XMLHttpRequest;u.addEventListener("load",(function(){var e=JSON.parse(u.response);u.status<200||u.status>=300?(e.status=u.status,a(e)):o(e,u.status)})),u.addEventListener("error",(function(){a({status:0,xhr:u})})),void 0!==r&&("GET"===e?t+="?"+(c=r,"?"+Object.keys(c).map((function(e){return e+"="+encodeURIComponent(c[e])})).join("&")):s=JSON.stringify(r)),u.open(e,i.url+t),void 0!==s&&u.setRequestHeader("Content-Type",n||"application/json;charset=UTF-8"),u.send(s)}))}},{key:"submitConsentData",value:function(e){return this.apiRequest("POST","/v1/privacy-managers/"+this.id+"/submit",e,"text/plain;charset=UTF-8")}},{key:"loadConfig",value:function(e){return this.apiRequest("GET","/v1/privacy-managers/"+this.id+"/config.json?name="+e+(this.opts.testing?"&testing=true":""))}},{key:"loadConfigs",value:function(){return this.apiRequest("GET","/v1/privacy-managers/"+this.id+"/configs.json"+(this.opts.testing?"&testing=true":""))}}])&&Nr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Rr=(r(7132),r(4062),r(5482));function Mr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,a,s=[],c=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(e,t)||qr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qr(e,t){if(e){if("string"==typeof e)return Ur(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ur(e,t):void 0}}function Ur(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Lr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Fr(e){for(var t=new Map([]),r=0,n=Object.keys(e);r<n.length;r++){var i=n[r],o=e[i];"string"==typeof i&&("string"==typeof o||null===o?t.set(i,o):t.set(i,Fr(o)))}return t}function Br(e,t,r,n){var i=function(e,t,r){if(r instanceof Map){var n=new Map([]);Br(n,r,!0,!1),e.set(t,n)}else e.set(t,r)};if(!(t instanceof Map&&e instanceof Map))throw new Error("Parameters are not maps!");void 0===r&&(r=!0),void 0===n&&(n=!1),n&&(e=new e.constructor(e));var o,a=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return Lr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Lr(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw o}}}}(t.keys());try{for(a.s();!(o=a.n()).done;){var s=o.value,c=t.get(s),u=e.get(s);if(e.has(s))if(c instanceof Map&&u instanceof Map)e.set(s,Br(u,c,r,n));else{if(!r)continue;i(e,s,c)}else i(e,s,c)}}catch(e){a.e(e)}finally{a.f()}return e}var Hr,Vr={top:{_meta:{incompatibleWith:["bottom"]},"notice-top":"20px","notice-bottom":"auto"},bottom:{_meta:{incompatibleWith:["top"]},"notice-bottom":"20px","notice-top":"auto"},left:{_meta:{incompatibleWith:["wide"]},"notice-left":"20px","notice-right":"auto"},right:{_meta:{incompatibleWith:["wide"]},"notice-right":"20px","notice-left":"auto"},wide:{"notice-left":"20px","notice-right":"auto","notice-max-width":"calc(100vw - 60px)","notice-position":"fixed"},light:{"button-text-color":"#fff",dark1:"#fafafa",dark2:"#777",dark3:"#555",light1:"#444",light2:"#666",light3:"#111",green3:"#f00"}};function Wr(e){return Wr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Wr(e)}function Kr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function $r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Kr(Object(r),!0).forEach((function(t){var n,i,o;n=e,i=t,o=r[t],i=function(e){var t=function(e,t){if("object"!=Wr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=Wr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Wr(t)?t:String(t)}(i),i in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Kr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Gr(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Zr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw o}}}}function Zr(e,t){if(e){if("string"==typeof e)return Yr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Yr(e,t):void 0}}function Yr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Jr=new Map([]),Xr={},Qr={};function en(e,t){return(e.elementID||"klaro")+(t?"-ide":"")}function tn(e,t){var r=en(e,t),n=document.getElementById(r);return null===n&&((n=document.createElement("div")).id=r,document.body.appendChild(n)),n}function rn(e,t){if(void 0===Xr[e]?Xr[e]=[t]:Xr[e].push(t),void 0!==Qr[e]){var r,n=Gr(Qr[e]);try{for(n.s();!(r=n.n()).done;){var i=r.value;if(!1===t.apply(void 0,function(e){if(Array.isArray(e))return Yr(e)}(o=i)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||Zr(o)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()))break}}catch(e){n.e(e)}finally{n.f()}}var o}function nn(e){for(var t=Xr[e],r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];if(void 0===Qr[e]?Qr[e]=[n]:Qr[e].push(n),void 0!==t){var o,a=Gr(t);try{for(a.s();!(o=a.n()).done;)if(!0===o.value.apply(void 0,n))return!0}catch(e){a.e(e)}finally{a.f()}}}function on(e){var t=new Map([]);return Br(t,Jr),Br(t,Fr(e.translations||{})),t}var an=1;function sn(e,t){if(void 0!==e){t=t||{},nn("render",e=ln(e),t);var r=0;t.show&&(r=an++);var n=tn(e),i=mn(e);void 0!==t.api&&i.watch(t.api),function(e,t,r){if(void 0!==e.styling){var n=Object.assign({},e.styling);if(void 0!==n.theme){var i=n.theme;i instanceof Array||(i=[i]),n={};var o,a=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=qr(e))){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw o}}}}(i);try{for(a.s();!(o=a.n()).done;){var s=t[o.value];if(void 0!==s)for(var c=0,u=Object.entries(s);c<u.length;c++){var l=Mr(u[c],2),p=l[0],f=l[1];p.startsWith("_")||(n[p]=f)}}}catch(e){a.e(e)}finally{a.f()}for(var d=0,v=Object.entries(e.styling);d<v.length;d++){var m=Mr(v[d],2),y=m[0],h=m[1];"theme"!==y&&(n[y]=h)}}void 0===r&&(r=document.documentElement);for(var b=0,g=Object.entries(n);b<g.length;b++){var _=Mr(g[b],2),w=_[0],k=_[1];r.style.setProperty("--"+w,k)}window.document.documentMode&&r===document.documentElement&&(0,Rr.N3)(n)}}(e,Vr,n);var o=st(e),a=on(e),s=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return ut.apply(void 0,[a,o,e.fallbackLang||"zz"].concat(r))},c=Ue(Qe.createElement(Or,{t:s,lang:o,manager:i,config:e,testing:t.testing,modal:t.modal,api:t.api,show:r}),n);return cn(i,s,o,e,t),c}}function cn(e,t,r,n,i){var o,a=[],s=Gr(n.services);try{for(s.s();!(o=s.n()).done;){var c,u=o.value,l=e.getConsent(u.name)&&e.confirmed,p=Gr(document.querySelectorAll("[data-name='"+u.name+"']"));try{for(p.s();!(c=p.n()).done;){var f=c.value,d=(0,Rr.RT)(f);if("placeholder"!==d.type&&("IFRAME"===f.tagName||"DIV"===f.tagName)){var v=f.previousElementSibling;if(null!==v){var m=(0,Rr.RT)(v);"placeholder"===m.type&&m.name===u.name||(v=null)}if(null===v){(v=document.createElement("DIV")).style.maxWidth=f.width+"px",v.style.height=f.height+"px",(0,Rr.X7)({type:"placeholder",name:u.name},v),l&&(v.style.display="none"),f.parentElement.insertBefore(v,f);var y=Ue(Qe.createElement(Pr,{t,lang:r,manager:e,config:n,service:u,style:d.style,testing:i.testing,api:i.api}),v);a.push(y)}"IFRAME"===f.tagName&&(d.src=f.src),void 0===d["modified-by-klaro"]&&void 0===f.style.display&&(d["original-display"]=f.style.display),d["modified-by-klaro"]="yes",(0,Rr.X7)(d,f),l||(f.src="",f.style.display="none")}}}catch(e){p.e(e)}finally{p.f()}}}catch(e){s.e(e)}finally{s.f()}return a}function un(e){/complete|interactive|loaded/.test(document.readyState)?e():window.addEventListener("DOMContentLoaded",e)}function ln(e){var t=$r({},e);return 2===t.version||(void 0!==t.apps&&void 0===t.services&&(t.services=t.apps,console.warn("Warning, your configuration file is outdated. Please change `apps` to `services`"),delete t.apps),void 0!==t.translations&&void 0!==t.translations.apps&&void 0===t.services&&(t.translations.services=t.translations.apps,console.warn("Warning, your configuration file is outdated. Please change `apps` to `services` in the `translations` key"),delete t.translations.apps)),t}function pn(e){if(void 0!==window){var t=(0,Rr.XZ)("klaro"),r=new Map(decodeURI(location.hash.slice(1)).split("&").map((function(e){return e.split("=")})).map((function(e){return 1===e.length?[e[0],!0]:e}))),n=r.get("klaro-testing"),i=function(e){var t=$r($r({},e),{},{testing:n});Hr.noAutoLoad||Hr.testing&&!t.testing||sn(Hr,t)};if(void 0!==e)Hr=e,un((function(){return i({})}));else if(null!==t){var o=function(e){var t=e.getAttribute("data-klaro-id");if(null!==t)return t;var r=/.*\/privacy-managers\/([a-f0-9]+)\/klaro.*\.js/.exec(e.src);return null!==r?r[1]:null}(t),a=function(e){var t=e.getAttribute("data-klaro-api-url");if(null!==t)return t;var r=/(http(?:s)?:\/\/[^/]+)\/v1\/privacy-managers\/([a-f0-9]+)\/klaro.*\.js/.exec(e.src);return null!==r?r[1]:null}(t),s=function(e,t){if(e.has("klaro-config"))return e.get("klaro-config");var r=t.getAttribute("data-klaro-config");return null!==r?r:"default"}(r,t);if(null!==o){var c=new Dr(a,o,{testing:n});if(void 0!==window.klaroApiConfigs){if(!0===nn("apiConfigsLoaded",window.klaroApiConfigs,c))return;var u=window.klaroApiConfigs.find((function(e){return e.name===s&&("active"===e.status||n)}));void 0!==u?(Hr=u,un((function(){return i({api:c})}))):nn("apiConfigsFailed",{})}else c.loadConfig(s).then((function(e){!0!==nn("apiConfigsLoaded",[e],c)&&(Hr=e,un((function(){return i({api:c})})))})).catch((function(e){console.error(e,"cannot load Klaro configs"),nn("apiConfigsFailed",e)}))}else{var l=t.getAttribute("data-klaro-config")||"klaroConfig";void 0!==(Hr=window[l])&&un((function(){return i({})}))}}r.has("klaro-ide")&&function(e){var t=/^(.*)(\/[^/]+)$/.exec(e.src)[1]||"",r=document.createElement("script");r.src=""!==t?t+"/ide.js":"ide.js",r.type="application/javascript";var n,i=Gr(r.attributes);try{for(i.s();!(n=i.n()).done;){var o=n.value;r.setAttribute(o.name,o.value)}}catch(e){i.e(e)}finally{i.f()}document.head.appendChild(r)}(t)}}function fn(e,t,r){return sn(e=e||Hr,{show:!0,modal:t,api:r}),!1}var dn={};function vn(){for(var e in Object.keys(dn))delete dn[e]}function mn(e){var t=(e=e||Hr).storageName||e.cookieName||"default";return void 0===dn[t]&&(dn[t]=new Ar.default(ln(e))),dn[t]}function yn(){return"v"==="v0.7.22"[0]?"v0.7.22".slice(1):"v0.7.22"}var hn=Fr({ca:{acceptAll:"Accepta-les totes",acceptSelected:"Accepta les escollides",service:{disableAll:{description:"Useu aquest botó per a habilitar o deshabilitar totes les aplicacions.",title:"Habilita/deshabilita totes les aplicacions"},optOut:{description:"Aquesta aplicació es carrega per defecte, però podeu desactivar-la",title:"(opt-out)"},purpose:"Finalitat",purposes:"Finalitats",required:{description:"Aquesta aplicació es necessita sempre",title:"(necessària)"}},close:"Tanca",consentModal:{description:"Aquí podeu veure i personalitzar la informació que recopilem sobre vós.",privacyPolicy:{name:"política de privadesa",text:"Per a més informació, consulteu la nostra {privacyPolicy}."},title:"Informació que recopilem"},consentNotice:{changeDescription:"Hi ha hagut canvis des de la vostra darrera visita. Actualitzeu el vostre consentiment.",description:"Recopilem i processem la vostra informació personal amb les següents finalitats: {purposes}.",imprint:{name:"Empremta"},learnMore:"Saber-ne més",privacyPolicy:{name:"política de privadesa"}},decline:"Rebutja",ok:"Accepta",poweredBy:"Funciona amb Klaro!",purposeItem:{service:"aplicació",services:"aplicacions"},save:"Desa"},cs:{privacyPolicy:{name:"zásady ochrany soukromí",text:'Pro další informace si přečtete naše <tr-hint v="privacy policy">{privacyPolicy}</tr-hint>.'},consentModal:{title:"Služby, které bychom rádi využili",description:"Zde můžete posoudit a přizpůsobit služby, které bychom rádi na tomto webu používali. Máte to pod kontrolou! Povolte nebo zakažte služby, jak uznáte za vhodné."},consentNotice:{testing:"Testing mode!",changeDescription:"Od vaší poslední návštěvy došlo ke změnám, obnovte prosím svůj souhlas.",description:"„Dobrý den! Můžeme povolit některé další služby pro {purposes}? Svůj souhlas můžete kdykoliv změnit nebo odvolat.“","learnMore|capitalize":"Vyberu si"},účely:{functional:{"title|capitalize":"Poskytování služeb",description:"Tyto služby jsou nezbytné pro správné fungování tohoto webu. Nelze je zde deaktivovat, protože služba by jinak nefungovala správně.\n"},performance:{"title|capitalize":"Optimalizace výkonu",description:"V rámci těchto služeb jsou zpracovávány osobní údaje za účelem optimalizace služeb, které jsou na tomto webu poskytovány.\n"},marketing:{"title|capitalize":"Marketing",description:"V rámci těchto služeb jsou zpracovávány osobní údaje, aby se vám zobrazoval relevantní obsah o produktech, službách nebo tématech, které by vás mohly zajímat."},advertising:{"title|capitalize":"Reklama",description:"V rámci těchto služeb jsou zpracovávány osobní údaje, aby vám zobrazovaly personalizované nebo zájmově orientované reklamy."}},purposeItem:{service:"Jednoduchá služba <tr-snip></tr-snip> , kterou nainstaluji do svého počítače.",services:"Několik jednoduchých služeb <tr-snip></tr-snip> , které nainstaluji do svého počítače."},"ok|capitalize":"To je v pořádku",save:"uložit","decline|capitalize":"Nepřijímám",close:"zavřít",acceptAll:"přijmout vše",acceptSelected:"přijmout vybrané",service:{disableAll:{title:"povolit nebo zakázat všechny služby",description:"Pomocí tohoto přepínače můžete povolit nebo zakázat všechny služby."},optOut:{title:"(opt-out)",description:"Tato služba se načítá ve výchozím nastavení (ale můžete ji zrušit)"},required:{title:"(vždy vyžadováno)",description:"Tato služba je vždy vyžadována"},purposes:"Zpracování  pro účely <tr-snip></tr-snip>",purpose:"Zpracování pro účely <tr-snip></tr-snip>"},poweredBy:"Realizováno pomocí Klaro!",contextualConsent:{description:"Chcete načíst externí obsah dodávaný prostřednictvím {title}?",acceptOnce:"Ano",acceptAlways:"Vždy"}},da:{acceptAll:"Tillad alle",acceptSelected:"Tillad udvalgte",service:{disableAll:{description:"Brug denne kontakt til at aktivere/deaktivere alle apps.",title:"Aktiver/deaktiver alle applikatione"},optOut:{description:"Denne applikation indlæses som standard (men du kan deaktivere den)",title:"Opt-Out"},purpose:"Formål",purposes:"Formål",required:{description:"Denne applikation er altid nødvendig",title:"(Altid nødvendig)"}},close:"Luk",consentModal:{description:"Her kan du se og ændre, hvilke informationer vi gemmer om dig.",privacyPolicy:{name:"Flere informationer finde du under {privacyPolicy}",text:"databeskyttelseserklæring."},title:"Informationer, som vi gemmer"},consentNotice:{changeDescription:"Der har været ændringer siden dit sidste besøg. Opdater dit valg.",description:"Vi gemmer og behandler dine personlige oplysninger til følgende formål: {purposes}.",imprint:{name:""},learnMore:"Læs mere",privacyPolicy:{name:"Datenschutzerklärung"}},decline:"Afvis",ok:"Ok",poweredBy:"Realiseret med Klaro!",purposeItem:{service:"",services:""},save:"Gem"},de:{acceptAll:"Alle akzeptieren",acceptSelected:"Ausgewählte akzeptieren",close:"Schließen",consentModal:{description:"Hier können Sie die Dienste, die wir auf dieser Website nutzen möchten, bewerten und anpassen. Sie haben das Sagen! Aktivieren oder deaktivieren Sie die Dienste, wie Sie es für richtig halten.",privacyPolicy:{name:"Datenschutzerklärung",text:"Um mehr zu erfahren, lesen Sie bitte unsere {privacyPolicy}."},title:"Dienste, die wir nutzen möchten"},consentNotice:{changeDescription:"Seit Ihrem letzten Besuch gab es Änderungen, bitte erneuern Sie Ihre Zustimmung.",title:"Cookie-Einstellungen",description:"Hallo! Könnten wir bitte einige zusätzliche Dienste für {purposes} aktivieren? Sie können Ihre Zustimmung später jederzeit ändern oder zurückziehen.",imprint:{name:"Impressum"},learnMore:"Lassen Sie mich wählen",privacyPolicy:{name:"Datenschutzerklärung"},testing:"Testmodus!"},contextualConsent:{acceptAlways:"Immer",acceptOnce:"Ja",description:"Möchten Sie von {title} bereitgestellte externe Inhalte laden?",descriptionEmptyStore:"Um diesem Dienst dauerhaft zustimmen zu können, müssen Sie {title} in den {link} zustimmen.",modalLinkText:"Cookie-Einstellungen"},decline:"Ich lehne ab",ok:"Das ist ok",poweredBy:"Realisiert mit Klaro!",privacyPolicy:{name:"Datenschutzerklärung",text:"Um mehr zu erfahren, lesen Sie bitte unsere {privacyPolicy}."},purposeItem:{service:"Dienst",services:"Dienste"},purposes:{advertising:{description:"Diese Dienste verarbeiten persönliche Informationen, um Ihnen personalisierte oder interessenbezogene Werbung zu zeigen.",title:"Werbung"},functional:{description:"Diese Dienste sind für die korrekte Funktion dieser Website unerlässlich. Sie können sie hier nicht deaktivieren, da der Dienst sonst nicht richtig funktionieren würde.\n",title:"Dienstbereitstellung"},marketing:{description:"Diese Dienste verarbeiten persönliche Daten, um Ihnen relevante Inhalte über Produkte, Dienstleistungen oder Themen zu zeigen, die Sie interessieren könnten.",title:"Marketing"},performance:{description:"Diese Dienste verarbeiten personenbezogene Daten, um den von dieser Website angebotenen Service zu optimieren.\n",title:"Optimierung der Leistung"}},save:"Speichern",service:{disableAll:{description:"Mit diesem Schalter können Sie alle Dienste aktivieren oder deaktivieren.",title:"Alle Dienste aktivieren oder deaktivieren"},optOut:{description:"Diese Dienste werden standardmäßig geladen (Sie können sich jedoch abmelden)",title:"(Opt-out)"},purpose:"Zweck",purposes:"Zwecke",required:{description:"Dieser Service ist immer erforderlich",title:"(immer erforderlich)"}}},el:{acceptAll:"",acceptAll_en:"Accept all",acceptSelected:"",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Χρησιμοποίησε αυτό τον διακόπτη για να ενεργοποιήσεις/απενεργοποιήσεις όλες τις εφαρμογές.",title:"Για όλες τις εφαρμογές"},optOut:{description:"Είναι προκαθορισμένο να φορτώνεται, άλλα μπορεί να παραληφθεί",title:"(μη απαιτούμενο)"},purpose:"Σκοπός",purposes:"Σκοποί",required:{description:"Δεν γίνεται να λειτουργήσει σωστά η εφαρμογή χωρίς αυτό",title:"(απαιτούμενο)"}},close:"Κλείσιμο",consentModal:{description:"Εδώ μπορείς να δεις και να ρυθμίσεις τις πληροφορίες που συλλέγουμε σχετικά με εσένα.",privacyPolicy:{name:"Πολιτική Απορρήτου",text:"Για περισσότερες πληροφορίες, παρακαλώ διαβάστε την {privacyPolicy}."},title:"Πληροφορίες που συλλέγουμε"},consentNotice:{changeDescription:"Πραγματοποιήθηκαν αλλαγές μετά την τελευταία σας επίσκεψη παρακαλούμε ανανεώστε την συγκατάθεση σας.",description:"Συγκεντρώνουμε και επεξεργαζόμαστε τα προσωπικά δεδομένα σας για τους παρακάτω λόγους: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Περισσότερα",privacyPolicy:{name:"Πολιτική Απορρήτου"}},decline:"Απόρριπτω",ok:"OK",poweredBy:"Υποστηρίζεται από το Klaro!",purposeItem:{service:"",services:""},save:"Αποθήκευση"},en:{acceptAll:"Accept all",acceptSelected:"Accept selected",close:"Close",consentModal:{description:"Here you can assess and customize the services that we'd like to use on this website. You're in charge! Enable or disable services as you see fit.",title:"Services we would like to use"},consentNotice:{changeDescription:"There were changes since your last visit, please renew your consent.",title:"Cookie Consent",description:"Hi! Could we please enable some additional services for {purposes}? You can always change or withdraw your consent later.",learnMore:"Let me choose",testing:"Testing mode!"},contextualConsent:{acceptAlways:"Always",acceptOnce:"Yes",description:"Do you want to load external content supplied by {title}?",descriptionEmptyStore:"To agree to this service permanently, you must accept {title} in the {link}.",modalLinkText:"Consent Manager"},decline:"I decline",ok:"That's ok",poweredBy:"Realized with Klaro!",privacyPolicy:{name:"privacy policy",text:"To learn more, please read our {privacyPolicy}."},purposeItem:{service:"service",services:"services"},purposes:{advertising:{description:"These services process personal information to show you personalized or interest-based advertisements.",title:"Advertising"},functional:{description:"These services are essential for the correct functioning of this website. You cannot disable them here as the service would not work correctly otherwise.\n",title:"Service Provision"},marketing:{description:"These services process personal information to show you relevant content about products, services or topics that you might be interested in.",title:"Marketing"},performance:{description:"These services process personal information to optimize the service that this website offers.\n",title:"Performance Optimization"}},save:"Save",service:{disableAll:{description:"Use this switch to enable or disable all services.",title:"Enable or disable all services"},optOut:{description:"This services is loaded by default (but you can opt out)",title:"(opt-out)"},purpose:"purpose",purposes:"purposes",required:{description:"This services is always required",title:"(always required)"}}},zh:{acceptAll:"照单全收",acceptSelected:"接受选择",close:"密切",consentModal:{description:"在这里，您可以评估和定制我们希望在本网站上使用的服务。您是负责人！您可以根据自己的需要启用或禁用服务。启用或禁用您认为合适的服务。",privacyPolicy:{name:"隐私政策",text:"要了解更多，请阅读我们的{privacyPolicy} 。"},title:"我们想使用的服务"},consentNotice:{changeDescription:"自上次访问后有变化，请更新您的同意。",description:"你好！我们可以为{purposes} 启用一些额外的服务吗？您可以随时更改或撤回您的同意。",imprint:{name:"印记"},learnMore:"让我来选",privacyPolicy:{name:"隐私政策"},testing:"测试模式！"},contextualConsent:{acceptAlways:"总是",acceptOnce:"是的，是的",description:"你想加载由{title} 提供的外部内容吗？"},decline:"我拒绝",ok:"没事的",poweredBy:"与Klaro一起实现!",privacyPolicy:{name:"隐私政策",text:"要了解更多，请阅读我们的{privacyPolicy} 。"},purposeItem:{service:"服务",services:"服务"},purposes:{advertising:{description:"这些服务处理个人信息，向您展示个性化或基于兴趣的广告。",title:"广告宣传"},functional:{description:"这些服务对于本网站的正常运行是必不可少的。您不能在这里禁用它们，否则服务将无法正常运行。\n",title:"服务提供"},marketing:{description:"这些服务会处理个人信息，向您展示您可能感兴趣的产品、服务或主题的相关内容。",title:"市场营销"},performance:{description:"这些服务处理个人信息是为了优化本网站提供的服务。\n",title:"性能优化"}},save:"挽救",service:{disableAll:{description:"使用此开关可启用或禁用所有服务。",title:"启用或停用所有服务"},optOut:{description:"这个服务是默认加载的(但你可以选择退出)",title:"(选择退出)"},purpose:"目的",purposes:"目的",required:{description:"这种服务是必须的",title:"(总是需要)"}}},pt:{acceptAll:"Aceitar todos",acceptSelected:"Aceitar selecionados",close:"Fechar",consentModal:{description:"Aqui você pode avaliar e personalizar os serviços que gostaríamos de usar neste website. Você está no comando! Habilite ou desabilite os serviços como julgar conveniente.",privacyPolicy:{name:"política de privacidade",text:"Para saber mais, por favor, leia nossa {privacyPolicy}."},title:"Serviços que gostaríamos de utilizar"},consentNotice:{changeDescription:"Houve mudanças desde sua última visita, queira renovar seu consentimento.",description:"Olá! Poderíamos, por favor, habilitar alguns serviços adicionais para {purposes}? Você pode sempre mudar ou retirar seu consentimento mais tarde.",imprint:{name:"imprimir"},learnMore:"Deixe-me escolher",privacyPolicy:{name:"política de privacidade"},testing:"Modo de teste!"},contextualConsent:{acceptAlways:"Sempre",acceptOnce:"Sim",description:"Você deseja carregar conteúdo externo fornecido por {title}?"},decline:"Recusar",ok:"Aceito.",poweredBy:"Realizado com Klaro!",privacyPolicy:{name:"política de privacidade",text:"Para saber mais, por favor, leia nossa {privacyPolicy}."},purposeItem:{service:"serviço",services:"serviços"},purposes:{advertising:{description:"Esses serviços processam informações pessoais para mostrar a você anúncios personalizados ou baseados em interesses.",title:"Publicidade"},functional:{description:"Esses serviços são essenciais para o correto funcionamento deste website. Você não pode desativá-los aqui, pois de outra forma o serviço não funcionaria corretamente.\n",title:"Prestação de serviços"},marketing:{description:"Esses serviços processam informações pessoais para mostrar a você conteúdo relevante sobre produtos, serviços ou tópicos que possam ser do seu interesse.",title:"Marketing"},performance:{description:"Esses serviços processam informações pessoais para otimizar o serviço que este website oferece.\n",title:"Otimização do desempenho"}},save:"Salvar",service:{disableAll:{description:"Use essa chave para habilitar ou desabilitar todos os serviços.",title:"Habilitar ou desabilitar todos os serviços"},optOut:{description:"Estes serviços são carregados por padrão (mas o você pode optar por não participar).",title:"(opt-out)"},purpose:"Objetivo",purposes:"Objetivos",required:{description:"Esses serviços são sempre necessários",title:"(sempre necessário)"}}},es:{acceptAll:"Aceptar todas",acceptSelected:"Aceptar seleccionadas",close:"Cerrar",consentModal:{description:"Aquí puede evaluar y personalizar los servicios que nos gustaría utilizar en este sitio web. ¡Usted decide! Habilite o deshabilite los servicios como considere oportuno.",privacyPolicy:{name:"política de privacidad",text:"Para saber más, por favor lea nuestra {privacyPolicy}."},title:"Servicios que nos gustaría utilizar"},consentNotice:{changeDescription:"Ha habido cambios en las cookies desde su última visita. Debe renovar su consentimiento.",description:"¡Hola! ¿Podríamos habilitar algunos servicios adicionales para {purposes}? Siempre podrá cambiar o retirar su consentimiento más tarde.",imprint:{name:"Imprimir"},learnMore:"Quiero elegir",privacyPolicy:{name:"política de privacidad"},testing:"¡Modo de prueba!"},contextualConsent:{acceptAlways:"Siempre",acceptOnce:"Sí",description:"¿Quieres cargar el contenido externo suministrado por {title}?"},decline:"Descartar todas",ok:"De acuerdo",poweredBy:"¡Realizado con Klaro!",privacyPolicy:{name:"política de privacidad",text:"Para saber más, por favor lea nuestra {privacyPolicy}."},purposeItem:{service:"servicio",services:"servicios"},purposes:{advertising:{description:"Estos servicios procesan información personal para mostrarle anuncios personalizados o basados en intereses.",title:"Publicidad"},functional:{description:"Estos servicios son esenciales para el correcto funcionamiento de este sitio web. No puede desactivarlos ya que la página no funcionaría correctamente.",title:"Prestación de servicios"},marketing:{description:"Estos servicios procesan información personal para mostrarle contenido relevante sobre productos, servicios o temas que puedan interesarle.",title:"Marketing"},performance:{description:"Estos servicios procesan información personal para optimizar el servicio que ofrece este sitio.",title:"Optimización del rendimiento"}},save:"Guardar",service:{disableAll:{description:"Utilice este interruptor para activar o desactivar todos los servicios.",title:"Activar o desactivar todos los servicios"},optOut:{description:"Este servicio está habilitado por defecto (pero puede optar por lo contrario)",title:"(desactivar)"},purpose:"Finalidad",purposes:"Finalidades",required:{description:"Este servicio es necesario siempre",title:"(siempre requerido)"}}},fi:{acceptAll:"",acceptAll_en:"Accept all",acceptSelected:"",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Aktivoi kaikki päälle/pois.",title:"Valitse kaikki"},optOut:{description:"Ladataan oletuksena (mutta voit ottaa sen pois päältä)",title:"(ladataan oletuksena)"},purpose:"Käyttötarkoitus",purposes:"Käyttötarkoitukset",required:{description:"Sivusto vaatii tämän aina",title:"(vaaditaan)"}},close:"Sulje",consentModal:{description:"Voit tarkastella ja muokata sinusta keräämiämme tietoja.",privacyPolicy:{name:"tietosuojasivultamme",text:"Voit lukea lisätietoja {privacyPolicy}."},title:"Keräämämme tiedot"},consentNotice:{changeDescription:"Olemme tehneet muutoksia ehtoihin viime vierailusi jälkeen, tarkista ehdot.",description:"Keräämme ja käsittelemme henkilötietoja seuraaviin tarkoituksiin: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Lue lisää",privacyPolicy:{name:"tietosuojasivultamme"}},decline:"Hylkää",ok:"Hyväksy",poweredBy:"Palvelun tarjoaa Klaro!",purposeItem:{service:"",services:""},save:"Tallenna"},fr:{acceptAll:"Accepter tout",acceptSelected:"Accepter sélectionné",close:"Fermer",consentModal:{description:"Vous pouvez ici évaluer et personnaliser les services que nous aimerions utiliser sur ce site. C'est vous qui décidez ! Activez ou désactivez les services comme bon vous semble.",privacyPolicy:{name:"politique de confidentialité",text:"Pour en savoir plus, veuillez lire notre {privacyPolicy}."},title:"Services que nous souhaitons utiliser"},consentNotice:{changeDescription:"Il y a eu des changements depuis votre dernière visite, veuillez renouveler votre consentement.",description:"Bonjour ! Pourrions-nous activer des services supplémentaires pour {purposes}? Vous pouvez toujours modifier ou retirer votre consentement plus tard.",imprint:{name:"mentions légales"},learnMore:"Laissez-moi choisir",privacyPolicy:{name:"politique de confidentialité"},testing:"Mode test !"},contextualConsent:{acceptAlways:"Toujours",acceptOnce:"Oui",description:"Vous souhaitez charger un contenu externe fourni par {title}?"},decline:"Je refuse",ok:"C'est bon.",poweredBy:"Réalisé avec Klaro !",privacyPolicy:{name:"politique de confidentialité",text:"Pour en savoir plus, veuillez lire notre {privacyPolicy}."},purposeItem:{service:"service",services:"services"},purposes:{advertising:{description:"Ces services traitent les informations personnelles pour vous présenter des publicités personnalisées ou basées sur des intérêts.",title:"Publicité"},functional:{description:"Ces services sont essentiels au bon fonctionnement de ce site. Vous ne pouvez pas les désactiver ici car le service ne fonctionnerait pas correctement autrement.\n",title:"Prestation de services"},marketing:{description:"Ces services traitent les informations personnelles afin de vous présenter un contenu pertinent sur les produits, les services ou les sujets qui pourraient vous intéresser.",title:"Marketing"},performance:{description:"Ces services traitent les informations personnelles afin d'optimiser le service que ce site Web offre.\n",title:"Optimisation de la performance"}},save:"Enregistrer",service:{disableAll:{description:"Utilisez ce commutateur pour activer ou désactiver tous les services.",title:"Activer ou désactiver tous les services"},optOut:{description:"Ce service est chargé par défaut (mais vous pouvez le désactiver)",title:"(opt-out)"},purpose:"Objet",purposes:"Fins",required:{description:"Ce service est toujours nécessaire",title:"(toujours requis)"}}},gl:{acceptAll:"Aceptar todas",acceptSelected:"Aceptar seleccionadas",close:"Pechar",consentModal:{description:"Aquí pode avaliar e personalizar os servizos que nos gustaría utilizar neste sitio web. ¡Vostede decide! Habilite ou deshabilite os servicios como lle conveña.",privacyPolicy:{name:"política de privacidade",text:"Para saber máis, por favor lea a nosa {privacyPolicy}."},title:"Servizos que nos gustaría utilizar"},consentNotice:{changeDescription:"Houbo cambios nas cookies dende a súa última visita. Debe renovar o seu consentimento.",description:"¡Ola! ¿Poderíamos habilitar algúns servizos adicionais para {purposes}? Sempre poderá cambiar ou retirar o séu consentimento máis tarde.",imprint:{name:"Imprimir"},learnMore:"Quero elixir",privacyPolicy:{name:"política de privacidade"},testing:"¡Modo de proba!"},decline:"Descartar todas",ok:"De acordo",poweredBy:"¡Realizado con Klaro!",privacyPolicy:{name:"política de privacidade",text:"Para saber máis, por favor lea a nosa {privacyPolicy}."},purposeItem:{service:"servizo",services:"servizos"},purposes:{advertising:{description:"Estes servizos procesan información persoal para mostrarlle anuncios personalizados ou basados en intereses.",title:"Publicidade"},functional:{description:"Estes servizos son esenciais para o correcto funcionamiento deste sitio web. Non pode desactivalos xa que a páxina non funcionaría correctamente.",title:"Prestación de servizos"},marketing:{description:"Estes servizos procesan información persoal para mostrarlle contido relevante sobre produtos, servizos ou temas que poidan interesarlle.",title:"Marketing"},performance:{description:"Estes servizos procesan información persoal para optimizar o servizo que ofrece este sitio.",title:"Optimización do rendimento"}},save:"Gardar",service:{disableAll:{description:"Utilice este interruptor para activar ou desactivar todos os servizos.",title:"Activar ou desactivar todos os servizos"},optOut:{description:"Este servizo está habilitado por defecto (pero pode optar polo contrario)",title:"(desactivar)"},purpose:"Finalidade",purposes:"Finalidades",required:{description:"Este servizo é necesario sempre",title:"(sempre requirido)"}}},hu:{acceptAll:"Mind elfogad",acceptAll_en:"Accept all",acceptSelected:"Kiválasztottat elfogad",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Használja ezt a kapcsolót az összes alkalmazás engedélyezéséhez/letiltásához.",title:"Összes app átkapcsolása"},optOut:{description:"Ez az alkalmazás alapértelmezés szerint betöltött (de ki lehet kapcsolni)",title:"(leiratkozás)"},purpose:"Cél",purposes:"Célok",required:{description:"Ez az alkalmazás mindig szükséges",title:"(mindig szükséges)"}},close:"Elvet",consentModal:{description:"Itt láthatja és testreszabhatja az önről gyűjtött információkat.",privacyPolicy:{name:"adatvédelmi irányelveinket",text:"További információért kérjük, olvassa el az {privacyPolicy}."},title:"Információk, amiket gyűjtünk"},consentNotice:{changeDescription:"Az utolsó látogatás óta változások történtek, kérjük, frissítse a hozzájárulását.",description:"Személyes adatait összegyűjtjük és feldolgozzuk az alábbi célokra: {purposes}.",imprint:{name:"impresszum",name_en:"imprint"},learnMore:"Tudjon meg többet",privacyPolicy:{name:"adatvédelmi irányelveinket"}},contextualConsent:{acceptAlways:"Mindig",acceptOnce:"Igen",description:"Be akarod tölteni a {title} által szolgáltatott külső tartalmakat?"},decline:"Elutasít",ok:"Elfogad",poweredBy:"Powered by Klaro!",purposeItem:{service:"",services:""},save:"Mentés"},hr:{acceptAll:"",acceptAll_en:"Prihvati sve",acceptSelected:"",acceptSelected_en:"Prihvati odabrane",service:{disableAll:{description:"Koristite ovaj prekidač da omogućite/onemogućite sve aplikacije odjednom.",title:"Izmeijeni sve"},optOut:{description:"Ova aplikacija je učitana automatski (ali je možete onemogućiti)",title:"(onemogućite)"},purpose:"Svrha",purposes:"Svrhe",required:{description:"Ova aplikacija je uvijek obavezna",title:"(obavezna)"}},close:"Zatvori",consentModal:{description:"Ovdje možete vidjeti i podesiti informacije koje prikupljamo o Vama.",privacyPolicy:{name:"pravila privatnosti",text:"Za više informacije pročitajte naša {privacyPolicy}."},title:"Informacije koje prikupljamo"},consentNotice:{changeDescription:"Došlo je do promjena od Vaše posljednjeg posjećivanja web stranice, molimo Vas da ažurirate svoja odobrenja.",description:"Mi prikupljamo i procesiramo Vaše osobne podatke radi slijedećeg: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Saznajte više",privacyPolicy:{name:"pravila privatnosti"}},decline:"Odbij",ok:"U redu",poweredBy:"Pokreće Klaro!",purposeItem:{service:"",services:""},save:"Spremi"},it:{acceptAll:"Accettare tutti",acceptSelected:"Accettare selezionato",close:"Chiudi",consentModal:{description:"Qui può valutare e personalizzare i servizi che vorremmo utilizzare su questo sito web. È lei il responsabile! Abilitare o disabilitare i servizi come meglio crede.",privacyPolicy:{name:"informativa sulla privacy",text:"Per saperne di più, legga la nostra {privacyPolicy}."},title:"Servizi che desideriamo utilizzare"},consentNotice:{changeDescription:"Ci sono stati dei cambiamenti rispetto alla sua ultima visita, la preghiamo di rinnovare il suo consenso.",description:"Salve, possiamo attivare alcuni servizi aggiuntivi per {purposes}? Può sempre modificare o ritirare il suo consenso in un secondo momento.",imprint:{name:"impronta"},learnMore:"Lasciatemi scegliere",privacyPolicy:{name:"informativa sulla privacy"},testing:"Modalità di test!"},contextualConsent:{acceptAlways:"Sempre",acceptOnce:"Sì",description:"Vuole caricare contenuti esterni forniti da {title}?"},decline:"Rifiuto",ok:"Va bene così",poweredBy:"Realizzato con Klaro!",privacyPolicy:{name:"informativa sulla privacy",text:"Per saperne di più, legga la nostra {privacyPolicy}."},purposeItem:{service:"servizio",services:"servizi"},purposes:{advertising:{description:"Questi servizi elaborano le informazioni personali per mostrarle annunci pubblicitari personalizzati o basati su interessi.",title:"Pubblicità"},functional:{description:"Questi servizi sono essenziali per il corretto funzionamento di questo sito web. Non può disattivarli qui perché altrimenti il servizio non funzionerebbe correttamente.\n",title:"Fornitura di servizi"},marketing:{description:"Questi servizi elaborano le informazioni personali per mostrarle contenuti rilevanti su prodotti, servizi o argomenti che potrebbero interessarla.",title:"Marketing"},performance:{description:"Questi servizi elaborano le informazioni personali per ottimizzare il servizio offerto da questo sito web.\n",title:"Ottimizzazione delle prestazioni"}},save:"Salva",service:{disableAll:{description:"Utilizzi questo interruttore per attivare o disattivare tutti i servizi.",title:"Attivare o disattivare tutti i servizi"},optOut:{description:"Questo servizio è caricato di default (ma è possibile scegliere di non usufruirne)",title:"(opt-out)"},purpose:"Scopo dell",purposes:"Finalità",required:{description:"Questo servizio è sempre richiesto",title:"(sempre richiesto)"}}},nl:{acceptAll:"Accepteer alle",acceptSelected:"Geselecteerde",close:"Sluit",consentModal:{description:"Hier kunt u de diensten die wij op deze website willen gebruiken beoordelen en aanpassen. U heeft de leiding! Schakel de diensten naar eigen inzicht in of uit.",privacyPolicy:{name:"privacybeleid",text:"Voor meer informatie kunt u ons {privacyPolicy} lezen."},title:"Diensten die we graag willen gebruiken"},consentNotice:{changeDescription:"Er waren veranderingen sinds uw laatste bezoek, gelieve uw toestemming te hernieuwen.",description:"Hallo, kunnen wij u een aantal extra diensten aanbieden voor {purposes}? U kunt uw toestemming later altijd nog wijzigen of intrekken.",imprint:{name:"impressum"},learnMore:"Laat me kiezen",privacyPolicy:{name:"privacybeleid"},testing:"Testmodus!"},contextualConsent:{acceptAlways:"Altijd",acceptOnce:"Ja",description:"Wilt u externe content laden die door {title} wordt aangeleverd ?"},decline:"Ik weiger",ok:"Dat is oké",poweredBy:"Gerealiseerd met Klaro!",privacyPolicy:{name:"privacybeleid",text:"Voor meer informatie kunt u ons {privacyPolicy} lezen."},purposeItem:{service:"service",services:"diensten"},purposes:{advertising:{description:"Deze diensten verwerken persoonlijke informatie om u gepersonaliseerde of op interesse gebaseerde advertenties te tonen.",title:"Reclame"},functional:{description:"Deze diensten zijn essentieel voor het correct functioneren van deze website. U kunt ze hier niet uitschakelen omdat de dienst anders niet correct zou werken.\n",title:"Dienstverlening"},marketing:{description:"Deze diensten verwerken persoonlijke informatie om u relevante inhoud te tonen over producten, diensten of onderwerpen waarin u geïnteresseerd zou kunnen zijn.",title:"Marketing"},performance:{description:"Deze diensten verwerken persoonlijke informatie om de service die deze website biedt te optimaliseren.\n",title:"Optimalisatie van de prestaties"}},save:"Opslaan",service:{disableAll:{description:"Gebruik deze schakelaar om alle diensten in of uit te schakelen.",title:"Alle diensten in- of uitschakelen"},optOut:{description:"Deze diensten worden standaard geladen (maar u kunt zich afmelden)",title:"(opt-out)"},purpose:"Verwerkingsdoel",purposes:"Verwerkingsdoeleinden",required:{description:"Deze diensten zijn altijd nodig",title:"(altijd nodig)"}}},no:{acceptAll:"Godtar alle",acceptSelected:"Godtar valgt",service:{disableAll:{description:"Bruk denne for å skru av/på alle apper.",title:"Bytt alle apper"},optOut:{description:"Denne appen er lastet som standard (men du kan skru det av)",title:"(opt-out)"},purpose:"Årsak",purposes:"Årsaker",required:{description:"Denne applikasjonen er alltid påkrevd",title:"(alltid påkrevd)"}},close:"",close_en:"Close",consentModal:{description:"Her kan du se og velge hvilken informasjon vi samler inn om deg.",privacyPolicy:{name:"personvernerklæring",text:"For å lære mer, vennligst les vår {privacyPolicy}."},title:"Informasjon vi samler inn"},consentNotice:{changeDescription:"Det har skjedd endringer siden ditt siste besøk, vennligst oppdater ditt samtykke.",description:"Vi samler inn og prosesserer din personlige informasjon av følgende årsaker: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Lær mer",privacyPolicy:{name:"personvernerklæring"}},decline:"Avslå",ok:"OK",poweredBy:"Laget med Klaro!",purposeItem:{service:"",services:""},save:"Opslaan"},oc:{acceptAll:"Tot acceptar",acceptSelected:"Acceptar çò seleccionat",close:"Tampar",consentModal:{description:"Aquí podètz mesurar e personalizar los servicis que volriam utilizar sus aqueste site web. Avètz lo darrièr mot ! Activatz o desactivatz segon vòstra causida.",title:"Servicis que volriam utilizar"},consentNotice:{changeDescription:"I aguèt de modificacions dempuèi vòstra darrièra visita, mercés de repassar vòstre consentiment.",description:"Adieu ! Poiriam activar mai de servici per {purposes} ? Podètz totjorn modificar o tirar vòstre consentiment mai tard.",learnMore:"Me daissar causir",testing:"Mòde tèst !"},contextualConsent:{acceptAlways:"Totjorn",acceptOnce:"Òc",description:"Volètz cargar de contenguts extèrn provesits per {title} ?"},decline:"Refusi",ok:"Es bon",poweredBy:"Realizat amb Klaro !",privacyPolicy:{name:"politica de confidencialitat",text:"Per ne saber mai, vejatz nòstra {privacyPolicy}."},purposeItem:{service:"servici",services:"servicis"},purposes:{advertising:{description:"Aquestes servicis tractan d’informacions personalas per vos mostrar de reclamas personalizadas o basadas suls interèsses.",title:"Reclama"},functional:{description:"Aquestes servicis son essencials pel foncionament corrèct d’aqueste site web. Los podètz pas desactivar aquí pr’amor que lo servici foncionariá pas coma cal autrament.\n",title:"Servici de provision"},marketing:{description:"Aquestes servicis tractan d’informacions personalas per vos mostrar de contenguts a prepaus de produits, de servicis o tèmas que poirián vos interessar.",title:"Marketing"},performance:{description:"Aquestes servicis tractan d’informacions per optimizar lo servici qu’aqueste site web prepausa.\n",title:"Optimizacion de las performanças"}},save:"Salvar",service:{disableAll:{description:"Utilizatz aqueste alternator per activar o desactivar totes los servicis.",title:"Activar o desactivar totes los servicis"},optOut:{description:"Aqueste servici es cargar per defaut (mas lo podètz desactivar)",title:"(opt-out)"},purpose:"finalitat",purposes:"finalitat",required:{description:"Aqueste servici es totjorn requesit",title:"(totjorn requesit)"}}},ro:{acceptAll:"",acceptAll_en:"Accept all",acceptSelected:"",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Utilizați acest switch pentru a activa/dezactiva toate aplicațiile.",title:"Comutați între toate aplicațiile"},optOut:{description:"Această aplicație este încărcată în mod implicit (dar puteți renunța)",title:"(opt-out)"},purpose:"Scop",purposes:"Scopuri",required:{description:"Această aplicație este întotdeauna necesară",title:"(întotdeauna necesar)"}},close:"",close_en:"Close",consentModal:{description:"Aici puteți vedea și personaliza informațiile pe care le colectăm despre dvs.",privacyPolicy:{name:"politica privacy",text:"Pentru a afla mai multe, vă rugăm să citiți {privacyPolicy}."},title:"Informațiile pe care le colectăm"},consentNotice:{changeDescription:"Au existat modificări de la ultima vizită, vă rugăm să actualizați consimțământul.",description:"Colectăm și procesăm informațiile dvs. personale în următoarele scopuri: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Află mai multe",privacyPolicy:{name:"politica privacy"}},decline:"Renunță",ok:"OK",poweredBy:"Realizat de Klaro!",purposeItem:{service:"",services:""},save:"Salvează"},sr:{acceptAll:"",acceptAll_en:"Accept all",acceptSelected:"",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Koristite ovaj prekidač da omogućite/onesposobite sve aplikacije odjednom.",title:"Izmeni sve"},optOut:{description:"Ova aplikacija je učitana automatski (ali je možete onesposobiti)",title:"(onesposobite)"},purpose:"Svrha",purposes:"Svrhe",required:{description:"Ova aplikacija je uvek neophodna",title:"(neophodna)"}},close:"Zatvori",consentModal:{description:"Ovde možete videti i podesiti informacije koje prikupljamo o Vama.",privacyPolicy:{name:"politiku privatnosti",text:"Za više informacije pročitajte našu {privacyPolicy}."},title:"Informacije koje prikupljamo"},consentNotice:{changeDescription:"Došlo je do promena od Vaše poslednje posete, molimo Vas da ažurirate svoja odobrenja.",description:"Mi prikupljamo i procesiramo Vaše lične podatke radi sledećeg: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Saznajte više",privacyPolicy:{name:"politiku privatnosti"}},decline:"Odbij",ok:"U redu",poweredBy:"Pokreće Klaro!",purposeItem:{service:"",services:""},save:"Sačuvaj"},sr_cyrl:{consentModal:{title:"Информације које прикупљамо",description:"Овде можете видет и подесити информације које прикупљамо о Вама.\n",privacyPolicy:{name:"политику приватности",text:"За више информација прочитајте нашу {privacyPolicy}.\n"}},consentNotice:{changeDescription:"Дошло је до промена од Ваше последнје посете, молимо Вас да ажурирате своја одобрења.",description:"Ми прикупљамо и процесирамо Ваше личне податке ради следећег: {purposes}.\n",learnMore:"Сазнајте више",privacyPolicy:{name:"политику приватности"}},ok:"У реду",save:"Сачувај",decline:"Одбиј",close:"Затвори",service:{disableAll:{title:"Измени све",description:"Користите овај прекидач да омогућите/онеспособите све апликације одједном."},optOut:{title:"(онеспособите)",description:"Ова апликација је учитана аутоматски (али је можете онеспособити)"},required:{title:"(неопходна)",description:"Ова апликација је увек неопходна."},purposes:"Сврхе",purpose:"Сврха"},poweredBy:"Покреће Кларо!"},sv:{acceptAll:"Acceptera alla",acceptSelected:"Acceptera markerat",service:{disableAll:{description:"Använd detta reglage för att aktivera/avaktivera samtliga appar.",title:"Ändra för alla appar"},optOut:{description:"Den här appen laddas som standardinställning (men du kan avaktivera den)",title:"(Avaktivera)"},purpose:"Syfte",purposes:"Syften",required:{description:"Den här applikationen krävs alltid",title:"(Krävs alltid)"}},close:"Stäng",consentModal:{description:"Här kan du se och anpassa vilken information vi samlar om dig.",privacyPolicy:{name:"Integritetspolicy",text:"För att veta mer, läs vår {privacyPolicy}."},title:"Information som vi samlar"},consentNotice:{changeDescription:"Det har skett förändringar sedan ditt senaste besök, var god uppdatera ditt medgivande.",description:"Vi samlar och bearbetar din personliga data i följande syften: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Läs mer",privacyPolicy:{name:"Integritetspolicy"}},decline:"Avböj",ok:"OK",poweredBy:"Körs på Klaro!",purposeItem:{service:"",services:""},save:"Spara"},tr:{acceptAll:"",acceptAll_en:"Accept all",acceptSelected:"",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Toplu açma/kapama için bu düğmeyi kullanabilirsin.",title:"Tüm uygulamaları aç/kapat"},optOut:{description:"Bu uygulama varsayılanda yüklendi (ancak iptal edebilirsin)",title:"(isteğe bağlı)"},purpose:"Amaç",purposes:"Amaçlar",required:{description:"Bu uygulama her zaman gerekli",title:"(her zaman gerekli)"}},close:"Kapat",consentModal:{description:"Hakkınızda topladığımız bilgileri burada görebilir ve özelleştirebilirsiniz.",privacyPolicy:{name:"Gizlilik Politikası",text:"Daha fazlası için lütfen {privacyPolicy} sayfamızı okuyun."},title:"Sakladığımız bilgiler"},consentNotice:{changeDescription:"Son ziyaretinizden bu yana değişiklikler oldu, lütfen seçiminizi güncelleyin.",description:"Kişisel bilgilerinizi aşağıdaki amaçlarla saklıyor ve işliyoruz: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Daha fazla bilgi",privacyPolicy:{name:"Gizlilik Politikası"}},decline:"Reddet",ok:"Tamam",poweredBy:"Klaro tarafından geliştirildi!",purposeItem:{service:"",services:""},save:"Kaydet"},pl:{acceptAll:"Zaakceptuj wszystkie",acceptSelected:"Zaakceptuj wybrane",close:"Zamknij",consentModal:{description:"Tutaj mogą Państwo ocenić i dostosować usługi, które chcielibyśmy wykorzystać na tej stronie. Włączaj lub wyłączaj usługi według własnego uznania.",privacyPolicy:{name:"polityką prywatności",text:"Aby dowiedzieć się więcej, prosimy o zapoznanie się z naszą {privacyPolicy}."},title:"Usługi, z których chcielibyśmy skorzystać"},consentNotice:{changeDescription:"Od Twojej ostatniej wizyty nastąpiły zmiany, prosimy o odnowienie zgody.",description:"Czy możemy włączyć dodatkowe usługi dla {purposes}? W każdej chwili mogą Państwo później zmienić lub wycofać swoją zgodę.",imprint:{name:"Imprint"},learnMore:"Pozwól mi wybrać",privacyPolicy:{name:"polityka prywatności"},testing:"Tryb testowy!"},contextualConsent:{acceptAlways:"Zawsze",acceptOnce:"Tak",description:"Czy chcą Państwo załadować treści zewnętrzne dostarczane przez {title}?"},decline:"Odmawiam",ok:"Ok",poweredBy:"Technologia dostarczona przez Klaro",privacyPolicy:{name:"polityka prywatności",text:"Aby dowiedzieć się więcej, prosimy o zapoznanie się z naszą {privacyPolicy}."},purposeItem:{service:"usługa",services:"usługi"},purposes:{advertising:{description:"Usługi te przetwarzają dane osobowe w celu pokazania Państwu spersonalizowanych lub opartych na zainteresowaniach reklam.",title:"Reklama"},functional:{description:"Usługi te są niezbędne do prawidłowego funkcjonowania niniejszej strony internetowej. Nie mogą Państwo ich tutaj wyłączyć, ponieważ w przeciwnym razie strona nie działałaby prawidłowo.\n",title:"Świadczenie usług"},marketing:{description:"Usługi te przetwarzają dane osobowe w celu pokazania Państwu istotnych treści dotyczących produktów, usług lub tematów, którymi mogą być Państwo zainteresowani.",title:"Marketing"},performance:{description:"Usługi te przetwarzają dane osobowe w celu optymalizacji usług oferowanych przez tę stronę.\n",title:"Optymalizacja wydajności"}},save:"Zapisz",service:{disableAll:{description:"Za pomocą tego przełącznika można włączać lub wyłączać wszystkie usługi.",title:"Włącz lub wyłącz wszystkie usługi"},optOut:{description:"Ta usługa jest domyślnie załadowana (ale mogą Państwo z niej zrezygnować)",title:"(opt-out)"},purpose:"Cel",purposes:"Cele",required:{description:"Usługi te są zawsze wymagane",title:"(zawsze wymagane)"}}},ru:{acceptAll:"Принять всё",acceptSelected:"Принять выбранные",service:{disableAll:{description:"Используйте этот переключатель, чтобы включить/отключить все приложения.",title:"Переключить все приложения"},optOut:{description:"Это приложение включено по умолчанию (но вы можете отказаться)",title:"(отказаться)"},purpose:"Намерение",purposes:"Намерения",required:{description:"Это обязательное приложение",title:"(всегда обязательный)"}},close:"Закрыть",consentModal:{description:"Здесь вы можете просмотреть и настроить, какую информацию о вас мы храним.",privacyPolicy:{name:"Соглашение",text:"Чтобы узнать больше, пожалуйста, прочитайте наше {privacyPolicy}."},title:"Информация, которую мы сохраняем"},consentNotice:{changeDescription:"Со времени вашего последнего визита произошли изменения, обновите своё согласие.",description:"Мы собираем и обрабатываем вашу личную информацию для следующих целей: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Настроить",privacyPolicy:{name:"политика конфиденциальности"}},decline:"Отклонить",ok:"Принять",poweredBy:"Работает на Кларо!",purposeItem:{service:"",services:""},save:"Сохранить"}});Br(Jr,hn),pn()})(),n})()));