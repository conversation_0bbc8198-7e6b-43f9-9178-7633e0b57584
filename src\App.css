html {
  overflow-y: scroll;
}

.App {
  display: flex;
  flex-direction: column;
  text-align: center;
  min-height: 100vh;  /* Fallback */
  min-height: 100dvh; /* Dynamic viewport height for mobile */
  position: relative;
  padding-bottom: 60px;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh; /* Fallback */
  min-height: 100dvh; /* Dynamic viewport height */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

main {
  flex: 1;  
  padding-bottom: 5px;
}

#klaro,
.klaro .cm-modal {
  position: fixed !important;
  bottom: 1rem;
  left: 1rem;
  top: auto !important;
  right: auto !important;
  width: 350px !important;
  max-width: 90vw;
  z-index: 1301 !important;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.klaro .cm-modal {
  padding: 1rem;
  font-size: 0.9rem;
}

.klaro .cm-header h1 {
  font-size: 1.1rem;
}

.klaro .cm-footer {
  justify-content: flex-end;
}

#klaro-cookie-notice {
  position: fixed !important;
  bottom: 5rem;
  left: 1rem;
  top: auto !important;
  right: auto !important;
  width: 350px !important;
  max-width: 90vw;
  z-index: 1301 !important;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);  
}