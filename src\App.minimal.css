/* App.minimal.css - Only non-themeable styles */
html {
    overflow-y: scroll;
  }

  /* Animation specific styles that don't depend on theme */
  .App-logo {
    height: 40vmin;
    pointer-events: none;
  }

  @media (prefers-reduced-motion: no-preference) {
    .App-logo {
      animation: App-logo-spin infinite 20s linear;
    }
  }

  @keyframes App-logo-spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  main {
    flex: 1;
    padding-bottom: 5px;
  }

  /* Klaro cookie notice positioning and layout - colors will be handled by theme */
  #klaro,
  .klaro .cm-modal {
    position: fixed !important;
    bottom: 1rem;
    left: 1rem;
    top: auto !important;
    right: auto !important;
    width: 350px !important;
    max-width: 85vw;
    z-index: 1301 !important;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .klaro .cm-modal {
    padding: 1rem;
    font-size: 0.9rem;
  }

  .klaro .cm-header h1 {
    font-size: 1.1rem;
  }

  .klaro .cm-footer {
    justify-content: flex-end;
  }

  #klaro-cookie-notice {
    position: fixed !important;
    bottom: 5rem;
    left: 1rem;
    top: auto !important;
    right: auto !important;
    width: 350px !important;
    max-width: 85vw;
    z-index: 1301 !important;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  /* Improved responsiveness for Klaro on small screens */
  @media (max-width: 480px) {
    #klaro,
    .klaro .cm-modal,
    #klaro-cookie-notice {
      left: 50% !important;
      transform: translateX(-50%);
      width: calc(100% - 2rem) !important;
      width: calc(100dvw - 2rem) !important; /* Dynamic viewport width */
      max-width: calc(100% - 2rem);
      max-width: calc(100dvw - 2rem); /* Dynamic viewport width */
      padding: 0.75rem;
      bottom: 3.5rem !important; /* Ensure it doesn't overlap with footer */
    }

    .klaro .cm-modal {
      font-size: 0.85rem;
    }

    .klaro .cm-header h1 {
      font-size: 1rem;
      margin-top: 0;
      margin-bottom: 0.5rem;
    }

    .klaro .cm-body p {
      margin-bottom: 0.5rem;
      line-height: 1.3;
    }

    .klaro .cm-footer {
      flex-wrap: wrap;
      gap: 0.5rem;
      padding-top: 0.5rem;
    }

    .klaro .cm-btn {
      padding: 0.4rem 0.6rem;
      font-size: 0.85rem;
      margin: 0.25rem;
    }

    /* Improve service items display on small screens */
    .klaro .cm-services {
      padding: 0.5rem 0;
    }

    .klaro .cm-service {
      margin-bottom: 0.5rem;
      padding: 0.5rem;
    }

    .klaro .cm-service-title {
      font-size: 0.9rem;
      font-weight: bold;
    }

    .klaro .cm-service-description {
      font-size: 0.8rem;
      margin-top: 0.25rem;
    }

    /* Ensure text doesn't overflow */
    .klaro .cm-modal * {
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
  }

  /* Very small screens */
  @media (max-width: 320px) {
    #klaro,
    .klaro .cm-modal,
    #klaro-cookie-notice {
      width: calc(100% - 1rem) !important;
      max-width: calc(100% - 1rem);
      padding: 0.5rem;
      left: 50% !important;
      transform: translateX(-50%);
    }

    .klaro .cm-header h1 {
      font-size: 0.9rem;
    }

    .klaro .cm-body {
      padding: 0.25rem 0;
    }

    .klaro .cm-body p {
      font-size: 0.8rem;
      margin-bottom: 0.25rem;
    }

    .klaro .cm-btn {
      padding: 0.3rem 0.5rem;
      font-size: 0.8rem;
      margin: 0.2rem;
    }
  }